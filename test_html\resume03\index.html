<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>个人简历</title>
    <link rel="stylesheet" href="style.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
</head>
<body>
    <!-- <header>
        <div class="resume-header" style="background-color: #675bae; height: 100px; width: 100%;">
            <h1>个人简历sfsfsdfsdfsdfsdfsdf</h1>
    
        </div>
    </header> -->


    <div class="resume-container">
        <!-- 简历头部 -->
        <header class="resume-title">
            <div class="header-icons">
                <img class="icon-1" src="./img/flower.svg" alt="icon">
                <img class="icon-2" src="./img/flower.svg" alt="icon">
                <img class="icon-3" src="./img/flower.svg" alt="icon">
                <img class="icon-4" src="./img/flower.svg" alt="icon">
                <!-- <svg class="icon">
                    <use xlink:href="icons.svg#icon1"/>
                </svg> -->
                <!-- <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M20 4H4c-1.1 0-1.99.9-1.99 2L2 18c0 1.1.9 2 2 2h16c1.1 0 2-.9 2-2V6c0-1.1-.9-2-2-2zm0 14H4V8l8 5 8-5v10zm-8-7L4 6h16l-8 5z"/></svg>
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="24" height="24"><path d="M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27.67-.36 1.02-.24 1.12.37 2.33.57 3.57.57.55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55.45-1 1-1h3.5c.55 0 1 .45 1 1 0 1.25.2 2.45.57 3.57.11.35.03.74-.25 1.02l-2.2 2.2z"/></svg> -->
            </div>
            <h1></h1>
        </header>


        <div class="content">
            <!-- 左侧栏 -->
            <div class="left-column">
                <!-- 照片区域 -->
                <div class="photo-container">
                    <img id="avatar" src="placeholder-avatar.jpg" alt="个人照片">
                </div>
                
                <!-- 姓名 -->
                <div class="name-container">
                    <h1 id="name">姓名占位符</h1>
                </div>
                
                <!-- 联系方式 -->
                <div class="section" id="contact-section">
                    <div class="left-column-strip"> 
                    </div>

                    <div class="left-column-title">
                        <!-- <i class="fas fa-phone-alt"></i> -->
                        <img class="icon-1" src="./img/tel.svg" alt="icon">
                        <h2>联系方式</h2>
                    </div>
                    <div class="section-content" id="contact-section-content">
                        <p><span>电话：</span><span id="phone">电话占位符</span></p>
                        <p><span>邮箱：</span><span id="email">邮箱占位符</span></p>
                    </div>
                </div>
                
                <!-- 个人信息 -->
                <div class="section" id="personal-info-section">
                    <div class="left-column-strip"> 
                    </div>

                    <div class="left-column-title">
                        <!-- <i class="fas fa-user"></i> -->
                        <img class="icon-1" src="./img/info.svg" alt="icon">
                        <h2>个人信息</h2>
                    </div>
                    <div class="section-content">
                        <p><span>民族：</span><span id="ethnicity">民族占位符</span></p>
                        <p><span>籍贯：</span><span id="hometown">籍贯占位符</span></p>
                        <p><span>现居：</span><span id="current-location">现居占位符</span></p>
                        <p><span>求职意向：</span><span id="job-intention">求职意向占位符</span></p>
                        <p><span>生日：</span><span id="birthday">生日占位符</span></p>
                        <p><span>身高：</span><span id="height">身高占位符</span></p>
                        <p><span>学历：</span><span id="education-level">学历占位符</span></p>
                        <p><span>政治面貌：</span><span id="political-status">政治面貌占位符</span></p>
                    </div>
                </div>
            </div>
            
            <!-- 右侧栏 -->
            <div class="right-column">
                <!-- 教育背景 -->
                <div class="section" id="education-section">
                    <div class="section-tile-and-line">
                        <div class="section-title">
                            <i class="fas fa-graduation-cap"></i>
                            <h2>教育背景</h2>
                        </div>
                        <div class="section-title-line"></div>
                    </div>
                    <div class="section-content">
                        <div class="education-item">
                            <div class="education-header">
                                <span id="education-time">20XX.09-20XX.06</span>
                                <span id="education-school">XX大学</span>
                                <span id="education-major">XX专业（本科）</span>
                            </div>
                            <div class="education-courses">
                                <p><span>主修课程：</span><span id="main-courses">课程占位符</span></p>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- 在校经历 -->
                <div class="section" id="experience-section">
                    <div class="section-tile-and-line">
                        <div class="section-title">
                            <i class="fas fa-briefcase"></i>
                            <h2>在校经历</h2>
                        </div>
                        <div class="section-title-line"></div>
                    </div>
                    <div class="section-content" id="experience-content">
                        <!-- 经历项目将通过JavaScript动态生成 -->
                    </div>
                </div>
                
                <!-- 技能证书 -->
                <div class="section" id="skills-section">
                    <div class="section-tile-and-line">
                        <div class="section-title">
                            <i class="fas fa-certificate"></i>
                            <h2>技能证书</h2>
                        </div>
                        <div class="section-title-line"></div>
                    </div>
                    <div class="section-content">
                        <p><span>获得证书：</span><span id="certificates">证书占位符</span></p>
                        <p><span>个人技能：</span><span id="personal-skills">技能占位符</span></p>
                        <p id="skill-details">技能详情占位符</p>
                    </div>
                </div>
                
                <!-- 自我评价 -->
                <div class="section" id="self-evaluation-section">
                    <div class="section-tile-and-line">
                        <div class="section-title">
                            <i class="fas fa-comment"></i>
                            <h2>自我评价</h2>
                        </div>
                        <div class="section-title-line"></div>
                    </div>
                    <div class="section-content" id="self-evaluation-content">
                        <!-- 自我评价项目将通过JavaScript动态生成 -->
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="script.js"></script>
</body>
</html> 