<view class="container3">
  <!-- 顶部标题已经在导航栏显示 -->

  <!-- 名称输入区域 -->
  <view class="formItem3">
    <view class="label3 required">自定义名称三</view>
    <input class="input3"
           placeholder="请输入"
           value="{{custom3FormData.customName}}"
           bindinput="handleNameInput"/>
  </view>

  <!-- 时间选择区域 -->
  <view class="formItem3">
    <view class="label3">时间</view>
    <view class="datePicker3">
      <picker mode="date" fields="month" value="{{custom3FormData.startDate}}" bindchange="handleStartDateChange">
        <view class="pickerText3">{{custom3FormData.startDate || '请选择(选填)'}}</view>
      </picker>
      <text class="separator3">—</text>
      <picker mode="date" fields="month" value="{{custom3FormData.endDate}}" bindchange="handleEndDateChange">
        <view class="pickerText3">{{custom3FormData.endDate || '请选择(选填)'}}</view>
      </picker>
      <view class="toNow3" bindtap="setToNow">至今</view>
    </view>
  </view>

  <!-- 角色输入区域 -->
  <view class="formItem3">
    <view class="label3">角色</view>
    <input class="input3"
           placeholder="请输入(选填)"
           value="{{custom3FormData.role}}"
           bindinput="handleRoleInput"/>
  </view>

  <!-- 自定义内容区域 -->
  <view class="formItem3 contentArea3">
    <view class="label3">自定义内容</view>
    <view class="editorToolbar3">
      <view class="toolItem3" bindtap="handleBold">
        <text class="{{customFormStyle.isBold ? 'active3' : ''}}">B</text>
      </view>
      <view class="toolItem3" bindtap="handleItalic">
        <text class="{{customFormStyle.isItalic ? 'active3' : ''}}">I</text>
      </view>
      <view class="toolItem3" bindtap="handleUnderline">
        <text class="{{customFormStyle.isUnderline ? 'active3' : ''}}">U</text>
      </view>
      <view class="toolItem3" bindtap="handleList">
        <text class="{{customFormStyle.isList ? 'active3' : ''}}">≡</text>
      </view>
    </view>
    <textarea class="contentInput3"
              placeholder="请输入自定义的详细内容"
              value="{{custom3FormData.content}}"
              bindinput="handleContentInput"
              maxlength="-1"
              auto-height/>

  </view>

  <!-- 底部按钮组 -->
  <view class="buttonGroup3">
    <button class="saveBtn3" bindtap="saveContent">保存信息</button>
    <button class="deleteBtn3" bindtap="deleteContent">删除</button>
  </view>
</view>