/**
 * 简历相关API接口
 */
const request = require('./request');

/**
 * 生成简历预览图片
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @returns {Promise<ArrayBuffer>} 图片二进制数据
 */
function generatePreviewImage(resumeData, themeConfig, templateId) {
  const requestData = {
    resume_data: resumeData,
    theme_config: themeConfig,
    template_id: templateId
  };

  // 添加调试日志
  console.log('=== 预览图片API请求 ===');
  console.log('模板ID:', templateId);
  console.log('主题配置:', themeConfig);
  console.log('请求数据:', requestData);

  // 生成缓存参数，防止缓存
  const timestamp = Date.now();
  const randomParam = Math.floor(Math.random() * 1000000);
  const cacheParam = `t=${timestamp}&r=${randomParam}`;

  return request.request({
    url: `/resume/export-jpeg?${cacheParam}`,
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'arraybuffer',
    showLoading: false,
    showError: false,
    needAuth: true // 根据文档，认证是可选的
  });
}

/**
 * 生成PDF文件
 * @param {Object} resumeData 简历数据
 * @param {Object} themeConfig 主题配置
 * @param {string} templateId 模板ID
 * @returns {Promise<ArrayBuffer>} PDF二进制数据
 */
function generatePDF(resumeData, themeConfig, templateId) {
  const requestData = {
    resume_data: resumeData,
    theme_config: themeConfig,
    template_id: templateId
  };

  return request.request({
    url: '/resume/export-pdf',
    method: 'POST',
    data: requestData,
    header: {
      'Content-Type': 'application/json'
    },
    responseType: 'arraybuffer',
    showLoading: true,
    loadingText: '正在生成PDF...',
    showError: true,
    needAuth: true // 根据文档，认证是可选的
  });
}

module.exports = {
  generatePreviewImage,
  generatePDF
};
