.resume-preview {
  /* position: relative; */
  width: 100%;
  height: 100%;
  overflow: hidden;
  /* background-color: #f3f7fb; */
}

/* 图片预览容器 */
.preview-image-container {
  width: 100%;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  /* min-height: 100rpx; */
  padding: 0rpx;
}

/* 预览图片样式 */
.preview-image {
  width: 100%;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  border-radius: 10rpx;
}

/* 加载动画容器 */
.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.6);
}

/* 加载动画 */
.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.resume-theme {
  --theme-color: #2B6CB0;
  --font-size: 28rpx;
  --spacing: 1;
}