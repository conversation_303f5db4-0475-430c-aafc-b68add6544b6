# 简历数据处理逻辑测试说明

## 测试目标
验证简历数据从用户输入到服务端传输的完整流程，确保所有字段都能正确汇集和传递。

## 测试环境
- 微信开发者工具
- 本地开发环境
- 控制台调试

## 测试步骤

### 1. 数据输入测试
**目标**: 验证各个模块的数据输入和保存功能

#### 1.1 基本信息测试
- [ ] 进入 `pages/makeResume/basicInfo/basicInfo` 页面
- [ ] 填写完整的基本信息（姓名、性别、电话、邮箱等）
- [ ] 点击保存，检查控制台日志
- [ ] 验证数据是否正确保存到本地存储和resumeManager

#### 1.2 教育经历测试
- [ ] 进入 `pages/makeResume/education/education/education` 页面
- [ ] 添加多条教育经历记录
- [ ] 检查数据格式是否正确（school、major、degree、startDate、endDate）
- [ ] 验证数据保存逻辑

#### 1.3 工作经历测试
- [ ] 进入 `pages/makeResume/work/work/work` 页面
- [ ] 添加工作经历记录
- [ ] 验证字段映射（company、position、description等）

#### 1.4 技能特长测试
- [ ] 进入 `pages/makeResume/skills/skills` 页面
- [ ] 添加多个技能项
- [ ] 验证数组格式是否正确

#### 1.5 自定义模块测试
- [ ] 测试custom1、custom2、custom3模块
- [ ] 验证customName、role、content等字段

### 2. 数据加载测试
**目标**: 验证makeResume页面的数据加载和显示功能

#### 2.1 页面数据加载
- [ ] 打开makeResume页面
- [ ] 检查控制台中的数据加载日志
- [ ] 验证`loadCurrentResumeData()`方法的执行
- [ ] 确认数据格式转换是否正确

#### 2.2 模块显示验证
- [ ] 检查各个模块是否正确显示用户输入的数据
- [ ] 验证activeModules和availableModulesToAdd的过滤逻辑
- [ ] 确认模块顺序是否正确

### 3. 数据传递测试
**目标**: 验证从makeResume到makeCreateResume的数据传递

#### 3.1 生成简历按钮测试
- [ ] 点击"生成简历"按钮
- [ ] 检查控制台中的数据传递日志
- [ ] 验证`generateResume()`方法的数据格式转换
- [ ] 确认传递的数据结构符合服务端期望

#### 3.2 预览页面数据接收
- [ ] 检查makeCreateResume页面是否正确接收数据
- [ ] 验证数据解析是否成功
- [ ] 确认所有字段都正确传递

### 4. 服务端数据格式验证
**目标**: 验证发送到服务端的数据格式

#### 4.1 API调用测试
- [ ] 在预览页面切换模板，触发预览图片生成
- [ ] 检查`utils/api/resumeApi.js`中的调试日志
- [ ] 验证发送到服务端的数据格式
- [ ] 确认字段名和数据类型符合`server_side_resumeData_define.py`

#### 4.2 数据完整性检查
- [ ] 验证所有必填字段都有值
- [ ] 检查数组字段的格式（education、work、project等）
- [ ] 确认自定义模块的数据结构正确

## 关键检查点

### 数据格式转换检查
```javascript
// 检查这些转换是否正确执行
1. resumeManager格式 → makeResume页面格式
2. makeResume页面格式 → resumeManager格式  
3. makeResume页面格式 → 服务端格式
```

### 字段映射检查
| 模块 | 页面字段 | resumeManager字段 | 服务端字段 |
|------|----------|-------------------|------------|
| 教育经历 | education | educations | education |
| 工作经历 | work | works | work |
| 项目经历 | project | projects | project |
| 自定义1 | custom1 | customs | custom1 |

### 必要的调试日志
- [ ] `=== 保存简历数据 ===`
- [ ] `=== 数据格式转换 ===`
- [ ] `========== makeResume传出的数据 ==========`
- [ ] `=== 预览图片API请求 ===`

## 常见问题排查

### 1. 数据丢失问题
- 检查字段名映射是否正确
- 验证数据类型转换
- 确认数组和对象的处理逻辑

### 2. 格式不匹配问题
- 对比服务端期望格式
- 检查数据验证函数
- 确认必填字段的处理

### 3. 预览图片问题
- 验证模板ID传递
- 检查数据完整性
- 确认API调用参数

## 测试通过标准
- [ ] 所有用户输入的数据都能正确保存
- [ ] 数据在各个页面间正确传递
- [ ] 服务端接收到的数据格式完全正确
- [ ] 预览功能正常工作
- [ ] 没有数据丢失或格式错误
- [ ] 调试日志清晰完整

## 测试报告
测试完成后，请记录：
1. 发现的问题和解决方案
2. 数据流程的改进建议
3. 代码优化的机会
4. 用户体验的改进点
