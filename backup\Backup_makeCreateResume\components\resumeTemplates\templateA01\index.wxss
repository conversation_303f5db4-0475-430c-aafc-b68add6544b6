.resumeTemplateA01 {
  width: 100%;
  aspect-ratio: 1 / 1.4142;
  max-width: 794px;
  margin: 0 auto;
  padding: 20rpx 40rpx 40rpx 40rpx;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  color: var(--text-color, #333333);
  box-sizing: border-box;
  background: #FFFFFF;
  position: relative;
}

.resumeTemplateA01::before {
  content: '';
  position: absolute;
  left: 60rpx;
  top: 100rpx;  /* 从基本信息开始 */
  bottom: 40rpx; /* 到底部结束 */
  width: 1rpx;
  background-color: var(--theme-color, #2B6CB0);
  transform: scaleX(0.3);
  transform-origin: left;
}

.resume-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: -10rpx;
  margin-bottom: 20rpx;
}

.resume-title {
  text-align: left;
  font-size: 40rpx;
  font-weight: bold;
  color: var(--theme-color, #2B6CB0);
}

.icon-group {
  display: flex;
  align-items: center;
  gap: 18rpx;
}

.icon-circle {
  width: 18rpx;
  height: 18rpx;
  padding: 10rpx;
  border-radius: 50%;
  background-color: var(--theme-color, #2B6CB0);
}

@media print {
  .resumeTemplateA01 {
    width: 210mm;
    height: 297mm;
    padding: 10mm;
    margin: 0;
  }
}

.basic-info {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 20rpx;
  margin-top: -30rpx;
  padding: 0 20rpx;
}

.avatar {
  width: 150rpx;
  height: 200rpx;
  object-fit: cover;
}

.info-content {
  flex: 1.5;
  margin-right: 0;
  width: auto;
}

.info {
  display: flex;
  flex-wrap: wrap;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  color: var(--secondary-color, #666666);
  column-gap: 150rpx;
  row-gap: 10rpx;
}

.info text {
  white-space: normal;  /* 允许文本换行 */
  overflow: visible;    /* 允许内容溢出显示 */
  text-overflow: clip;  /* 移除省略号 */
  word-break: break-all; /* 允许在任意字符间断行 */
}

.section {
  margin-top: -60rpx;
  margin-bottom: 50rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
}

.title-wrapper {
  position: relative;
  margin-bottom: 20rpx;
  display: flex;
  align-items: center;
}

.title-wrapper:empty {
  display: none;
}

.title:empty + .title-bg {
  display: none;
}

.title-bg {
  position: absolute;
  left: -34rpx;
  top: 50%;
  width: 153%;
  height: 280rpx;
  z-index: 1;
  transform: translateY(-50%) scale(1.2);
  transform-origin: left center;
  background-color: var(--theme-color, #2B6CB0);
  -webkit-mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTg5MiIgaGVpZ2h0PSI0MzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8dGl0bGU+TGF5ZXIgMTwvdGl0bGU+CiAgPHJlY3QgaWQ9InN2Z18xIiBoZWlnaHQ9IjIwNiIgd2lkdGg9Ijg4NCIgeT0iODYiIHg9IjY1IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfMiIgeTI9IjI2NCIgeDI9IjcwMDAiIHkxPSIyNjQiIHgxPSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxwYXRoIHRyYW5zZm9ybT0icm90YXRlKDE4MCAxOTUgMzQwKSIgaWQ9InN2Z18zIiBkPSJtNjUsMzk3bDAsLTEzOWwyNjAsMTM5bC0yNjAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPHBhdGggaWQ9InN2Z183IiBkPSJtOTQwLDI5M2wwLC0yMDZsMjYwLDIwNmwtMjYwLDB6IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfOCIgeTI9IjI5MyIgeDI9IjEyNzEiIHkxPSI4NiIgeDE9IjEwMTQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzEwIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzExIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+") no-repeat center / contain;
  mask: url("data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTg5MiIgaGVpZ2h0PSI0MzkiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CiA8Zz4KICA8dGl0bGU+TGF5ZXIgMTwvdGl0bGU+CiAgPHJlY3QgaWQ9InN2Z18xIiBoZWlnaHQ9IjIwNiIgd2lkdGg9Ijg4NCIgeT0iODYiIHg9IjY1IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfMiIgeTI9IjI2NCIgeDI9IjcwMDAiIHkxPSIyNjQiIHgxPSI2NSIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxwYXRoIHRyYW5zZm9ybT0icm90YXRlKDE4MCAxOTUgMzQwKSIgaWQ9InN2Z18zIiBkPSJtNjUsMzk3bDAsLTEzOWwyNjAsMTM5bC0yNjAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPHBhdGggaWQ9InN2Z183IiBkPSJtOTQwLDI5M2wwLC0yMDZsMjYwLDIwNmwtMjYwLDB6IiBmaWxsPSJ3aGl0ZSIgc3Ryb2tlPSJ3aGl0ZSIvPgogIDxsaW5lIGlkPSJzdmdfOCIgeTI9IjI5MyIgeDI9IjEyNzEiIHkxPSI4NiIgeDE9IjEwMTQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzEwIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KICA8cGF0aCBpZD0ic3ZnXzExIiBkPSJtMTAxNCw4NmwyNjAsMjA2IiBvcGFjaXR5PSJ1bmRlZmluZWQiIHN0cm9rZT0id2hpdGUiIGZpbGw9Im5vbmUiLz4KIDwvZz4KPC9zdmc+") no-repeat center / contain;
}

.title {
  position: relative;
  z-index: 2;
  font-size: calc(var(--font-size, 12rpx) + 4rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  color: #FFFFFF;
  font-weight: bold;
  padding: 20rpx 0;
}

.content {
  margin-bottom: 15rpx;
  margin-top: -30rpx;
  padding: 0 20rpx;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  color: var(--text-color, #333333);
  gap: 10rpx;
  display: flex;
  flex-wrap: wrap;
}

.job-intention-content {
  display: flex;
  flex-wrap: wrap;
  gap: 15rpx;
  line-height: calc(var(--spacing, 1) * 1.3);
}

.job-intention-item {
  width: calc(50% - 10rpx);
  box-sizing: border-box;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
}

.school, .company, .projectName, .custom-name, .activity {
  font-weight: bold;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.degree, .time {
  color: var(--secondary-color, #666666);
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  font-weight: bold;
}

.courses {
  color: var(--text-color, #333333);
  margin-top: 5rpx;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
}

.skill, .award, .interest {
  display: inline-block;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  color: var(--text-color, #333333);
}

.awardInfo {
  color: var(--secondary-color, #666666);
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
}

.interestName {
  font-weight: bold;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
}

.description {
  color: var(--text-color, #333333);
  margin-top: 5rpx;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  width: 100%;
}

.tixing-wrapper {
  position: relative;
  margin: 20rpx 0;
  height: 40rpx;
  width: 100%;
  display: flex;
  align-items: center;
}

.tixing-container1 {
  position: relative;
  width: 120%;
  height: 90%;
}

.tixing-container2 {
  position: relative;
  width: 70%;
  height: 100%;
  margin-left: -10%;
}

.tixing-icon {
  width: 120%;
  height: 25rpx;
  background-color: var(--theme-color, #2B6CB0);
  position: absolute;
  left: -140rpx;
  top: -20rpx;
  z-index: 2;
  -webkit-mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
  mask: url("data:image/svg+xml;base64,PD94bWwgdmVyc2lvbj0iMS4wIiBlbmNvZGluZz0iVVRGLTgiPz4KPHN2ZyB3aWR0aD0iMTAwMCIgaGVpZ2h0PSI1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIiB2aWV3Qm94PSIwIDAgMTAwMCA1MCI+CiAgPGc+CiAgICA8cmVjdCBpZD0ic3ZnXzEiIGhlaWdodD0iMzAiIHdpZHRoPSI5NDYiIHk9IjEwIiB4PSIxMCIgZmlsbD0id2hpdGUiIHN0cm9rZT0id2hpdGUiLz4KICAgIDxwYXRoIGlkPSJzdmdfMiIgZD0ibTk1Niw0MGwwLC0zMGwzMCwzMGwtMzAsMHoiIGZpbGw9IndoaXRlIiBzdHJva2U9IndoaXRlIi8+CiAgPC9nPgo8L3N2Zz4=") no-repeat center / contain;
}

.tixing2-image {
  width: 140%;
  height: 30rpx;
  position: absolute;
  left: -50rpx;
  top: -16rpx;
  z-index: 1;
  transform: scale(1);
  transform-origin: left center;
}

.background-title {
  width: 100%;
  margin: 30rpx 0;
  position: relative;
  height: 40rpx;
}

.background-title-image {
  width: 120%;
  height: 30rpx;
  position: absolute;
  left: -140rpx;
  display: block;
  z-index: 2;
}

.info-columns {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.info-column {
  flex: 0 0 48%;
  display: flex;
  flex-direction: column;
  gap: var(--info-gap, 10rpx);
}

.info-column:last-child {
  margin-left: var(--column-gap, 100rpx);
}

.info-column text {
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 2rpx 0;
}

.education-header {
  display: flex;
  align-items: center;
  width: 100%;
  margin-bottom: 10rpx;
  min-height: 1.5em;
}

.school {
  font-weight: bold;
  width: 35%;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.major-degree {
  font-weight: bold;
  width: 35%;
  text-align: center;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.edu-date {
  width: 30%;
  text-align: right;
  color: var(--secondary-color, #666666);
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: bold;
}

.courses {
  margin-top: 5rpx;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  color: var(--text-color, #333333);
}

.courses-label {
  font-weight: bold;
}

.content + .content {
  margin-top: calc(var(--spacing, 1) * 10rpx);
}

.school-experience-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 5rpx;
}

.school-experience-header .time {
  width: 30%;
  text-align: right;
  color: var(--secondary-color, #666666);
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: bold;
}

.activity {
  font-weight: bold;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  flex: 1;
}

.time {
  color: var(--secondary-color, #666666);
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  text-align: right;
  font-weight: bold;
}

.description {
  color: var(--text-color, #333333);
  margin-top: 5rpx;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  width: 100%;
}

.internship-header {
  display: flex;
  align-items: center;
  width: 100%;
}

.internship-header .company:first-child {
  flex: 0 0 35%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.internship-header .company:nth-child(2) {
  flex: 0 0 35%;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.internship-header .time {
  flex: 0 0 30%;
  text-align: right;
  color: var(--secondary-color, #666666);
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: bold;
}

.custom-name {
  font-weight: bold;
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  flex: 1;
}

.custom-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  margin-bottom: 5rpx;
}

.custom-header .time {
  width: 30%;
  text-align: right;
  color: var(--secondary-color, #666666);
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-weight: bold;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10rpx;
  width: 100%;
}

.info-item {
  font-size: var(--font-size, 12rpx);
  line-height: calc(var(--spacing, 1) * 1.3);
  color: var(--text-color, #333333);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: block;
}

.edu-description {
  color: var(--text-color, #333333);
  margin-top: 5rpx;
  white-space: pre-wrap;
  word-break: break-all;
  font-size: var(--font-size, 12rpx);
  width: 100%;
}

.edu-description text {
  display: block;
  line-height: calc(var(--spacing, 1) * 1.3);
}

.edu-description text + text {
  margin-top: 5rpx;
}

.edu-description .courses-label {
  display: inline;
  font-weight: bold;
}

/* 移除不需要的SVG样式 */