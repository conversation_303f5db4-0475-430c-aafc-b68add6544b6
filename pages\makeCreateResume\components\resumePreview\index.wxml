<scroll-view 
  class="resume-preview resume-theme" 
  style="{{customStyle}}"
  scroll-y="true"
  enhanced="true"
  show-scrollbar="false"
  bounces="true"
  enable-flex="true"
>
  <!-- 使用服务端生成的图片预览 -->
  <view wx:if="{{previewImageUrl}}" class="preview-image-container">
    <image 
      class="preview-image" 
      src="{{previewImageUrl}}" 
      mode="widthFix"
      binderror="onImageError"
    />
    <view wx:if="{{imageLoading}}" class="loading-container">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>
  </view>
  
  <!-- 动态使用不同模板（当图片预览不可用时的备选项） -->
  <view wx:else>
    <templateA01 
      id="templateA01"
      wx:if="{{currentTemplate === 'templateA01'}}"
      resumeData="{{currentResumeData}}"
      config="{{lastConfig}}"
    />
    <templateA02
      id="templateA02"
      wx:if="{{currentTemplate === 'templateA02'}}"
      resumeData="{{currentResumeData}}"
      config="{{lastConfig}}"
    />
    <templateA03
      id="templateA03"
      wx:if="{{currentTemplate === 'templateA03'}}"
      resumeData="{{currentResumeData}}"
      config="{{lastConfig}}"
    />
  </view>
</scroll-view> 