Page({
  data: {
    educationEditFormData: {
      school: '',      // 学校名称
      major: '',       // 专业
      degree: '',      // 学历
      startDate: '',   // 开始时间
      endDate: '',     // 结束时间
      description: '', // 描述
      sortIndex: 0    // 添加排序字段
    },
    editIndex: -1,
    degreeArray: ['高中', '大专', '本科', '硕士', '博士'],  // 学历选项
    degreeIndex: 2    // 选中的学历索引，默认选中本科
  },

  onLoad(options) {
    if (options.index) {
      const savedData = wx.getStorageSync('education') || [];
      const editIndex = parseInt(options.index);

      if (savedData[editIndex]) {
        this.setData({
          educationEditFormData: savedData[editIndex],
          editIndex: editIndex,
          degreeIndex: this.data.degreeArray.indexOf(savedData[editIndex].degree)
        });
      }
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`educationEditFormData.${field}`]: value
    });
  },

  // 学历选择变化时触发
  handleDegreeChange(e) {
    const index = e.detail.value;
    this.setData({
      'educationEditFormData.degree': this.data.degreeArray[index],
      degreeIndex: index
    });
  },

  // 日期选择变化时触发
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;

    this.setData({
      [`educationEditFormData.${field}`]: value
    });
  },

  // 设置结束时间为"至今"
  setEndDateToNow() {
    this.setData({
      'educationEditFormData.endDate': '至今'
    });
  },

  // 保存教育经历
  saveEducation() {
    const { educationEditFormData, editIndex } = this.data;

    // 验证必填字段
    if (!educationEditFormData.school || !educationEditFormData.major || !educationEditFormData.degree) {
      wx.showToast({
        title: '请填写完整信息',
        icon: 'none'
      });
      return;
    }

    // 获取已保存的数据
    const savedData = wx.getStorageSync('education') || [];

    if (editIndex >= 0) {
      // 更新已有记录，保持原有的sortIndex
      const originalSortIndex = savedData[editIndex].sortIndex || editIndex;
      savedData[editIndex] = {
        ...educationEditFormData,
        sortIndex: originalSortIndex
      };
    } else {
      // 添加新记录，sortIndex为当前数组长度
      savedData.push({
        ...educationEditFormData,
        sortIndex: savedData.length
      });
    }

    // 保存更新后的数据
    wx.setStorageSync('education', savedData);

    // 同时保存到resumeManager
    const resumeManager = require('../../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新教育经历
    currentResumeData.educations = savedData;

    // 保存到resumeManager
    resumeManager.saveCurrentResumeData(currentResumeData);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 删除教育经历
  deleteEducation() {
    const { editIndex } = this.data;
    if (editIndex >= 0) {
      wx.showModal({
        title: '提示',
        content: '确定删除这条教育经历吗？',
        success: (res) => {
          if (res.confirm) {
            const savedData = wx.getStorageSync('education') || [];
            savedData.splice(editIndex, 1);
            wx.setStorageSync('education', savedData);

            wx.showToast({
              title: '已删除',
              icon: 'success',
              duration: 1500,
              success: () => {
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              }
            });
          }
        }
      });
    }
  }
});