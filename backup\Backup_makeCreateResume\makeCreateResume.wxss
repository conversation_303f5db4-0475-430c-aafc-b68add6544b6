/* pages/makeCreateResume/makeCreateResume.wxss */
.resume-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #F2F2F2;
}

.preview-area {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx;
  background: #F2F2F2;
}

.template-area {
  height: 200rpx;
  background: #fff;
  padding: 20rpx;
  border-top: 1rpx solid #eee;
}

.toolbar-area {
  height: 100rpx;
}

/* 隐藏HTML生成器 */
.hidden-generator {
  position: absolute;
  left: -9999px;
  visibility: hidden;
  pointer-events: none;
}

.container {
  min-height: 100vh;
  background: #F2F2F2;
  padding: 20rpx;
  box-sizing: border-box;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.generate-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #4B8BF5;
  color: white;
  border-radius: 8rpx;
  font-size: 32rpx;
} 