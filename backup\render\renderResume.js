/**
 * 简历模板渲染系统
 */

// 导入模板
let resumeTemplates;
if (typeof require !== 'undefined') {
  resumeTemplates = require('./templates').resumeTemplates;
} else if (typeof window !== 'undefined') {
  resumeTemplates = window.resumeTemplates;
}

/**
 * 简单的模板引擎，替换模板中的变量
 * @param {string} template - 包含{{变量}}的模板字符串
 * @param {Object} data - 包含变量值的对象
 * @returns {string} - 替换变量后的字符串
 */
function renderTemplate(template, data) {
  return template.replace(/\{\{(\w+)\}\}/g, function(match, key) {
    return data[key] !== undefined ? data[key] : '';
  });
}

/**
 * 渲染简历数据到指定模板
 * @param {Object} userData - 用户简历数据
 * @param {string} templateId - 模板ID，例如'template03'
 * @param {string} containerId - 简历容器的ID (可选)
 * @returns {Object} - 返回渲染后的HTML和CSS
 */
function renderResume(userData, templateId = 'template03', containerId = 'resume-container') {
  // 获取指定的模板
  const template = resumeTemplates[templateId];
  if (!template) {
    throw new Error(`Template "${templateId}" not found`);
  }
  
  // 创建一个深拷贝的数据对象，避免修改原始数据
  const resumeData = JSON.parse(JSON.stringify(userData));
  
  // 处理基本信息
  const basicInfo = resumeData.basicInfo || {};
  const name = basicInfo.name || resumeData.name || '';
  const avatar = basicInfo.photoUrl || resumeData.avatar || 'placeholder-avatar.jpg';
  const phone = basicInfo.phone || resumeData.phone || '';
  const email = basicInfo.email || resumeData.email || '';
  const gender = basicInfo.gender || '';
  const age = basicInfo.age || '';
  const marriage = basicInfo.marriage || '';
  const nation = basicInfo.nation || '';
  const wechat = basicInfo.wechat || '';
  const weight = basicInfo.weight || '';
  
  // 处理个人信息
  const personalInfo = resumeData.personalInfo || {};
  const ethnicity = personalInfo.ethnicity || basicInfo.nation || '';
  const hometown = personalInfo.hometown || basicInfo.hometown || '';
  const currentLocation = personalInfo.currentLocation || basicInfo.city || '';
  const birthday = personalInfo.birthday || basicInfo.birthday || '';
  const height = personalInfo.height || basicInfo.height || '';
  const educationLevel = personalInfo.educationLevel || '';
  const politicalStatus = personalInfo.politicalStatus || basicInfo.politics || '';
  
  // 处理求职意向
  const jobIntention = resumeData.jobIntention || {};
  const position = jobIntention.position || '';
  const salary = jobIntention.salary || '';
  const jobCity = jobIntention.city || '';
  const jobStatus = jobIntention.status || '';
  
  // 处理教育背景
  const education = resumeData.education || resumeData.school || [];
  
  // 处理工作经历
  const workExperience = resumeData.work || resumeData.internship || [];
  
  // 处理项目经历
  const projectExperience = resumeData.project || [];
  
  // 处理技能证书
  const skills = resumeData.skills || [];
  
  // 处理奖项荣誉
  const awards = resumeData.awards || [];
  
  // 处理兴趣爱好
  const interests = resumeData.interests || [];
  
  // 处理自我评价
  const evaluations = resumeData.evaluation || [];
  
  // 处理自定义模块
  const custom = resumeData.custom || {};
  const custom1 = custom.custom1 && custom.custom1[0] ? {
    title: custom.custom1[0].customName || '自定义模块1',
    role: custom.custom1[0].role || '',
    startDate: custom.custom1[0].startDate || '',
    endDate: custom.custom1[0].endDate || '',
    content: custom.custom1[0].content || ''
  } : null;
  
  const custom2 = custom.custom2 && custom.custom2[0] ? {
    title: custom.custom2[0].customName || '自定义模块2',
    role: custom.custom2[0].role || '',
    startDate: custom.custom2[0].startDate || '',
    endDate: custom.custom2[0].endDate || '',
    content: custom.custom2[0].content || ''
  } : null;
  
  const custom3 = custom.custom3 && custom.custom3[0] ? {
    title: custom.custom3[0].customName || '自定义模块3',
    role: custom.custom3[0].role || '',
    startDate: custom.custom3[0].startDate || '',
    endDate: custom.custom3[0].endDate || '',
    content: custom.custom3[0].content || ''
  } : null;
  
  // 生成模块HTML
  let educationHTML = '';
  let workHTML = '';
  let projectHTML = '';
  let skillsHTML = '';
  let awardsHTML = '';
  let interestsHTML = '';
  let evaluationHTML = '';
  let custom1HTML = '';
  let custom2HTML = '';
  let custom3HTML = '';
  
  // 如果模板提供了特定的渲染函数，则使用模板的渲染函数
  if (template.renderEducation && education.length > 0) {
    educationHTML = template.renderEducation(education);
  }
  
  if (template.renderWork && workExperience.length > 0) {
    workHTML = template.renderWork(workExperience);
  }
  
  if (template.renderProject && projectExperience.length > 0) {
    projectHTML = template.renderProject(projectExperience);
  }
  
  if (template.renderSkills && skills.length > 0) {
    skillsHTML = template.renderSkills(skills);
  }
  
  if (template.renderAwards && awards.length > 0) {
    awardsHTML = template.renderAwards(awards);
  }
  
  if (template.renderInterests && interests.length > 0) {
    interestsHTML = template.renderInterests(interests);
  }
  
  if (template.renderEvaluation && evaluations.length > 0) {
    evaluationHTML = template.renderEvaluation(evaluations);
  }
  
  if (template.renderCustom) {
    if (custom1) custom1HTML = template.renderCustom(custom1, 1);
    if (custom2) custom2HTML = template.renderCustom(custom2, 2);
    if (custom3) custom3HTML = template.renderCustom(custom3, 3);
  }
  
  // 确定哪些部分应该隐藏
  const hideBasicInfo = !name && !phone && !email && !gender && !age && !marriage && !nation && !wechat && !weight ? 'hidden' : '';
  const hidePersonalInfo = !ethnicity && !hometown && !currentLocation && !birthday && !height && !educationLevel && !politicalStatus ? 'hidden' : '';
  const hideJobIntention = !position && !salary && !jobCity && !jobStatus ? 'hidden' : '';
  const hideEducation = !education || education.length === 0 ? 'hidden' : '';
  const hideWork = !workExperience || workExperience.length === 0 ? 'hidden' : '';
  const hideProject = !projectExperience || projectExperience.length === 0 ? 'hidden' : '';
  const hideSkills = !skills || skills.length === 0 ? 'hidden' : '';
  const hideAwards = !awards || awards.length === 0 ? 'hidden' : '';
  const hideInterests = !interests || interests.length === 0 ? 'hidden' : '';
  const hideEvaluation = !evaluations || evaluations.length === 0 ? 'hidden' : '';
  const hideCustom1 = !custom1 ? 'hidden' : '';
  const hideCustom2 = !custom2 ? 'hidden' : '';
  const hideCustom3 = !custom3 ? 'hidden' : '';
  
  // 准备模板数据
  const templateData = {
    containerId,
    // 基本信息
    name,
    avatar,
    phone,
    email,
    gender,
    age,
    marriage,
    nation,
    wechat,
    weight,
    // 个人信息
    ethnicity,
    hometown,
    currentLocation,
    birthday,
    height,
    educationLevel,
    politicalStatus,
    // 求职意向
    position,
    salary,
    jobCity,
    jobStatus,
    // 各模块HTML
    educationHTML,
    workHTML,
    projectHTML,
    skillsHTML,
    awardsHTML,
    interestsHTML,
    evaluationHTML,
    custom1HTML,
    custom2HTML,
    custom3HTML,
    // 隐藏标记
    hideBasicInfo,
    hidePersonalInfo,
    hideJobIntention,
    hideEducation,
    hideWork,
    hideProject,
    hideSkills,
    hideAwards,
    hideInterests,
    hideEvaluation,
    hideCustom1,
    hideCustom2,
    hideCustom3
  };
  
  // 渲染HTML模板
  const html = renderTemplate(template.html, templateData);
  
  // 如果在浏览器环境中，可以直接更新DOM
  if (typeof document !== 'undefined') {
    const container = document.getElementById(containerId);
    if (container) {
      container.outerHTML = html;
      
      // 添加样式
      if (!document.getElementById(`${templateId}-style`)) {
        const styleElement = document.createElement('style');
        styleElement.id = `${templateId}-style`;
        styleElement.textContent = template.css;
        document.head.appendChild(styleElement);
      }
      
      return { success: true };
    }
  }
  
  // 返回渲染后的HTML和CSS
  return {
    html,
    css: template.css
  };
}

/**
 * 生成完整的HTML文档
 * @param {Object} userData - 用户简历数据
 * @param {string} templateId - 模板ID
 * @returns {string} - 完整的HTML文档
 */
function generateFullHTML(userData, templateId = 'template03') {
  const result = renderResume(userData, templateId);
  
  return `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>${userData.name || userData.basicInfo?.name || '个人'}的简历</title>
  <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
  <style>
    ${result.css}
  </style>
</head>
<body>
  ${result.html}
</body>
</html>
  `;
}

// 导出函数，以便在Node.js环境中使用
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { renderResume, generateFullHTML };
}