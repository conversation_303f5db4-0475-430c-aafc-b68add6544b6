// pages/user/settings/settings.js
const apiConfig = require('../../../config/apiConfig');
const app = getApp();

Page({
  data: {
    userInfo: null,
    hasUserInfo: false,
    isLoading: false,
    // 设置项
    settings: {
      autoSave: true,
      dataSync: true,
      usageTracking: true
    }
  },

  onLoad() {
    // 检查登录状态
    if (!app.checkLogin('/pages/user/settings/settings')) {
      return;
    }

    // 加载用户信息和设置
    this.loadUserInfo();
    this.loadUserSettings();
  },

  onShow() {
    // 每次显示页面时刷新数据
    if (app.globalData.hasUserInfo) {
      this.loadUserInfo();
    }
  },

  // 加载用户信息
  loadUserInfo() {
    const userInfo = wx.getStorageSync('userInfo');
    if (userInfo) {
      this.setData({
        userInfo: userInfo,
        hasUserInfo: true
      });
    }
  },

  // 加载用户设置
  loadUserSettings() {
    const userId = app.getUserId();
    if (!userId) return;

    this.setData({ isLoading: true });

    // 先从本地存储加载设置
    const localSettings = wx.getStorageSync('userSettings');
    if (localSettings) {
      this.setData({
        settings: localSettings
      });
    }

    // 然后从服务器获取最新设置
    wx.request({
      url: apiConfig.userInfoUrl,
      method: 'GET',
      data: { userId: userId },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
      },
      success: (res) => {
        if (res.data.success && res.data.settings) {
          // 更新设置
          this.setData({
            settings: res.data.settings
          });

          // 保存到本地存储
          wx.setStorageSync('userSettings', res.data.settings);
        }
      },
      fail: (err) => {
        console.error('获取用户设置失败:', err);
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 切换设置项
  toggleSetting(e) {
    const settingKey = e.currentTarget.dataset.key;
    const newValue = !this.data.settings[settingKey];

    // 更新本地状态
    this.setData({
      [`settings.${settingKey}`]: newValue
    });

    // 保存设置到服务器
    this.saveSettings();
  },

  // 保存设置
  saveSettings() {
    const userId = app.getUserId();
    if (!userId) return;

    this.setData({ isLoading: true });

    wx.request({
      url: apiConfig.updateUserInfoUrl,
      method: 'POST',
      data: {
        userId: userId,
        settings: this.data.settings
      },
      header: {
        'Authorization': `Bearer ${wx.getStorageSync('userToken')}`
      },
      success: (res) => {
        if (res.data.success) {
          // 保存到本地存储
          wx.setStorageSync('userSettings', this.data.settings);

          // 记录用户行为
          app.trackUserAction('update_settings', {
            settings: this.data.settings
          });

          wx.showToast({
            title: '设置已保存',
            icon: 'success'
          });
        } else {
          console.error('保存设置失败:', res.data.message);
          wx.showToast({
            title: '保存失败',
            icon: 'none'
          });
        }
      },
      fail: (err) => {
        console.error('保存设置请求失败:', err);
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        });
      },
      complete: () => {
        this.setData({ isLoading: false });
      }
    });
  },

  // 清除缓存
  clearCache() {
    wx.showModal({
      title: '提示',
      content: '确定要清除本地缓存吗？这不会删除您的账号数据。',
      success: (res) => {
        if (res.confirm) {
          try {
            // 保留登录信息
            const userInfo = wx.getStorageSync('userInfo');
            const userId = wx.getStorageSync('userId');
            const userToken = wx.getStorageSync('userToken');
            const userSettings = wx.getStorageSync('userSettings');

            // 清除所有存储
            wx.clearStorageSync();

            // 恢复登录信息
            wx.setStorageSync('userInfo', userInfo);
            wx.setStorageSync('userId', userId);
            wx.setStorageSync('userToken', userToken);
            wx.setStorageSync('userSettings', userSettings);

            // 记录用户行为
            app.trackUserAction('clear_cache');

            wx.showToast({
              title: '缓存已清除',
              icon: 'success'
            });
          } catch (e) {
            console.error('清除缓存失败:', e);
            wx.showToast({
              title: '清除缓存失败',
              icon: 'none'
            });
          }
        }
      }
    });
  },

  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {


          // 调用app的登出方法
          app.logout();

          wx.showToast({
            title: '已退出登录',
            icon: 'success',
            success: () => {
              // 返回上一页
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        }
      }
    });
  },

  // 关于我们
  showAbout() {
    wx.showModal({
      title: '关于我们',
      content: '个人简历模板Offer必来\n版本: 1.0.0\n\n一款专业的简历制作工具，帮助您轻松创建精美简历，提高求职成功率。',
      showCancel: false
    });
  },

  // 用户协议
  showUserAgreement() {
    wx.navigateTo({
      url: '/pages/user/agreement/agreement'
    });
  },

  // 隐私政策
  showPrivacyPolicy() {
    wx.navigateTo({
      url: '/pages/user/privacy/privacy'
    });
  }
})
