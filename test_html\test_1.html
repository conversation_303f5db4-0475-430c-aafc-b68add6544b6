<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>页面布局示例</title>
  <style>
    /* 全局样式设置 */
    body {
      margin: 0;
      padding: 0;
    }

    /* 顶部横幅样式 */
    header {
      background-color: #333;
      color: white;
      text-align: center;
      padding: 20px;
    }

    /* 主体容器样式 */
    .main-container {
      display: flex;
      margin: 0;
    }

    /* 主体左侧区域样式 */
    .left-section {
      background-color: #f4f4f4;
      width: 30%;
      padding: 20px;
    }

    /* 主体右侧区域样式 */
    .right-section {
      background-color: #b03232;
      width: 70%;
      padding: 20px;
    }

    /* 底部区域样式 */
    footer {
      background-color: #333;
      color: white;
      text-align: center;
      padding: 20px;
    }
  </style>
</head>

<body>
  <!-- 顶部横幅 -->
  <header>
    <h1>这是顶部横幅</h1>
  </header>

  <!-- 主体部分 -->
  <div class="main-container">
    <!-- 主体左侧 -->
    <div class="left-section">
      <p>这是左侧内容区域</p>
    </div>
    <!-- 主体右侧 -->
    <div class="right-section">
      <p>这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域这是右侧内sdddddddddddddddddsdddddddddd容区域</p>
    </div>
  </div>

  <!-- 底部区域 -->
  <footer>
    <p>这是底部内容</p>
    <p>ssfsdfsdfsdf</p>
    <p>hahahasfsfs</p>
  </footer>
</body>

</html>