Page({
  data: {
    projectEditFormData: {
      projectName: '',  // 项目名称
      role: '',        // 担任角色
      startDate: '',   // 开始时间
      endDate: '',     // 结束时间
      description: ''  // 项目描述
    },
    isEdit: false,
    editIndex: -1
  },

  onLoad(options) {
    if (options.index) {
      const index = parseInt(options.index);
      const projectList = wx.getStorageSync('projectList') || [];
      const projectEditFormData = projectList[index];

      this.setData({
        projectEditFormData,
        isEdit: true,
        editIndex: index
      });
    }
  },

  // 处理输入
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`projectEditFormData.${field}`]: e.detail.value
    });
  },

  // 处理日期选择
  handleDateChange(e) {
    const { field } = e.currentTarget.dataset;
    this.setData({
      [`projectEditFormData.${field}`]: e.detail.value
    });
  },

  // 设置结束日期为"至今"
  setEndDateToNow() {
    this.setData({
      'projectEditFormData.endDate': '至今'
    });
  },

  // 保存信息
  saveInfo() {
    const { projectEditFormData, isEdit, editIndex } = this.data;

    // 表单验证
    if (!projectEditFormData.projectName) {
      wx.showToast({
        title: '请输入项目名称',
        icon: 'none'
      });
      return;
    }
    if (!projectEditFormData.role) {
      wx.showToast({
        title: '请输入担任角色',
        icon: 'none'
      });
      return;
    }
    if (!projectEditFormData.startDate) {
      wx.showToast({
        title: '请选择开始时间',
        icon: 'none'
      });
      return;
    }
    if (!projectEditFormData.endDate) {
      wx.showToast({
        title: '请选择结束时间',
        icon: 'none'
      });
      return;
    }

    let projectList = wx.getStorageSync('projectList') || [];

    if (isEdit) {
      projectList[editIndex] = projectEditFormData;
    } else {
      projectList.push(projectEditFormData);
    }

    wx.setStorageSync('projectList', projectList);

    // 同时保存到resumeManager
    const resumeManager = require('../../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新项目经历
    currentResumeData.projects = projectList;

    // 保存到resumeManager
    resumeManager.saveCurrentResumeData(currentResumeData);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 删除
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除该项目经历吗？',
      success: (res) => {
        if (res.confirm) {
          const { editIndex } = this.data;
          let projectList = wx.getStorageSync('projectList') || [];
          projectList.splice(editIndex, 1);
          wx.setStorageSync('projectList', projectList);

          wx.navigateBack({
            success: () => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
            }
          });
        }
      }
    });
  }
});