<view class="container">
  <view class="formGroup">
    <!-- 必填项 -->
    <view class="formItem">
      <text class="label">姓名</text>
      <input class="input" placeholder="请输入" value="{{basicInfoFormData.name}}" data-field="name" bindinput="handleInput"/>
    </view>

    <view class="formItem">
      <text class="label">性别</text>
      <picker class="picker" range="{{genderArray}}" value="{{genderIndex === -1 ? 0 : genderIndex}}" bindchange="handleGenderPicker">
        <view class="pickerArea">
          <view class="pickerText">{{basicInfoFormData.gender || '请选择'}}</view>
        </view>
      </picker>
      <text class="deleteIcon" bindtap="clearField" data-field="gender">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">电话</text>
      <input class="input" type="number" placeholder="请输入" value="{{basicInfoFormData.phone}}" data-field="phone" bindinput="handleInput"/>
    </view>

    <view class="sectionTitle">以下选填</view>

    <!-- 选填项 -->
    <view class="formItem">
      <text class="label">现居城市</text>
      <input class="input" placeholder="请输入 最多15字符" maxlength="15" value="{{basicInfoFormData.city}}" data-field="city" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="city">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">邮箱</text>
      <input class="input" type="text" placeholder="请输入" value="{{basicInfoFormData.email}}" data-field="email" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="email">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">微信</text>
      <input class="input" placeholder="请输入" value="{{basicInfoFormData.wechat}}" data-field="wechat" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="wechat">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">年龄</text>
      <input class="input" type="number" placeholder="请输入" value="{{basicInfoFormData.age}}" data-field="age" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="age">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">出生年月</text>
      <picker class="picker" mode="date" value="{{basicInfoFormData.birthday}}" data-field="birthday" bindchange="handlePicker">
        <view class="pickerText">{{basicInfoFormData.birthday || '请选择'}}</view>
      </picker>
      <text class="deleteIcon" bindtap="clearField" data-field="birthday">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">婚姻状况</text>
      <picker class="picker" range="{{marriageArray}}" value="{{marriageIndex === -1 ? 0 : marriageIndex}}" bindchange="handleMarriagePicker">
        <view class="pickerText">{{basicInfoFormData.marriage || '请选择'}}</view>
      </picker>
      <text class="deleteIcon" bindtap="clearField" data-field="marriage">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">政治面貌</text>
      <picker class="picker" range="{{politicsArray}}" value="{{politicsIndex === -1 ? 0 : politicsIndex}}" bindchange="handlePoliticsPicker">
        <view class="pickerText">{{basicInfoFormData.politics || '请选择'}}</view>
      </picker>
      <text class="deleteIcon" bindtap="clearField" data-field="politics">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">民族</text>
      <input class="input" placeholder="请输入" value="{{basicInfoFormData.nation}}" data-field="nation" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="nation">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">籍贯</text>
      <input class="input" placeholder="请输入 最多15字符" maxlength="15" value="{{basicInfoFormData.hometown}}" data-field="hometown" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="hometown">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">身高(CM)</text>
      <input class="input" type="number" placeholder="请输入" value="{{basicInfoFormData.height}}" data-field="height" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="height">🗑️</text>
    </view>

    <view class="formItem">
      <text class="label">体重(KG)</text>
      <input class="input" type="number" placeholder="请输入" value="{{basicInfoFormData.weight}}" data-field="weight" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearField" data-field="weight">🗑️</text>
    </view>

    <!-- 自定义字段 -->
    <view class="formItem">
      <input class="input customTitle" placeholder="自定义标题" value="{{basicInfoFormData.customTitle1}}" data-field="customTitle1" bindinput="handleInput"/>
      <input class="input" placeholder="自定义内容" value="{{basicInfoFormData.customContent1}}" data-field="customContent1" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearCustomField" data-index="1">🗑️</text>
    </view>

    <view class="formItem">
      <input class="input customTitle" placeholder="自定义标题" value="{{basicInfoFormData.customTitle2}}" data-field="customTitle2" bindinput="handleInput"/>
      <input class="input" placeholder="自定义内容" value="{{basicInfoFormData.customContent2}}" data-field="customContent2" bindinput="handleInput"/>
      <text class="deleteIcon" bindtap="clearCustomField" data-index="2">🗑️</text>
    </view>
  </view>

  <view class="photoSection">
    <view class="photoContainer" bindtap="chooseImage">
      <view class="photoUpload" wx:if="{{!basicInfoFormData.photoUrl}}">
        <view class="uploadPlaceholder">
          <text class="uploadIcon">+</text>
          <text class="uploadText">添加证件照</text>
        </view>
      </view>
      <image wx:else class="photo" src="{{basicInfoFormData.photoUrl}}" mode="aspectFill"></image>
    </view>
  </view>

  <view class="buttonGroup">
    <button class="saveBtn" bindtap="saveInfo">保存信息</button>
    <button class="deleteBtn" bindtap="deleteInfo">删除</button>
  </view>
</view> 