/* pages/new_makeCreateResume/new_makeCreateResume.wxss */
/* 基础样式 */
page {
    background-color: #f0f2f5;
    height: 100%;
  }
  
  .resume-preview-page {
    display: flex;
    flex-direction: column;
    height: 100%;
  }
  
  /* 顶部导航 */
  .nav-header {
    background-color: #4B8BF5;
    color: white;
    padding: 20rpx 30rpx;
    text-align: center;
    position: relative;
  }
  
  .nav-title {
    font-size: 36rpx;
    font-weight: 500;
  }


/* 简历预览区域 */
.resume-preview-container {
    flex: 1;
    padding: 20rpx;
    overflow: hidden;
  }
  
  .resume-preview-wrapper {
    background-color: white;
    border-radius: 16rpx;
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
    height: 100%;
    position: relative;
    overflow: hidden;
  }
  
  .resume-content {
    height: 100%;
    overflow-y: auto;
    padding: 20rpx;
  }
  
  /* 加载状态 */
  .loading-mask {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10;
  }
  
  .loading-spinner {
    width: 80rpx;
    height: 80rpx;
    border: 6rpx solid #f3f3f3;
    border-top: 6rpx solid #4B8BF5;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
  
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

/* 模板选择区域 */
.template-selector {
    background-color: white;
    padding: 20rpx 0;
    border-top: 1rpx solid #eee;
    margin-bottom: 100rpx; /* 为底部工具栏留出空间 */
  }
  
  .template-scroll {
    white-space: nowrap;
    padding: 0 20rpx;
  }
  
  .template-item {
    display: inline-block;
    width: 180rpx;
    margin-right: 20rpx;
    text-align: center;
    vertical-align: top;
  }
  
  .template-thumbnail {
    width: 180rpx;
    height: 240rpx;
    border-radius: 8rpx;
    border: 2rpx solid #eee;
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  }
  
  .template-item.active .template-thumbnail {
    border: 2rpx solid #4B8BF5;
    box-shadow: 0 2rpx 12rpx rgba(75, 139, 245, 0.3);
  }
  
  .template-name {
    font-size: 24rpx;
    color: #666;
    margin-top: 10rpx;
  }
  
  .template-item.active .template-name {
    color: #4B8BF5;
    font-weight: 500;
  }

/* 样式设置面板 */
.style-panel {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: white;
    border-top-left-radius: 20rpx;
    border-top-right-radius: 20rpx;
    box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.1);
    transform: translateY(100%);
    transition: transform 0.3s ease;
    z-index: 100;
    padding-bottom: 120rpx; /* 为底部工具栏留出空间 */
  }
  
  .style-panel.show {
    transform: translateY(0);
  }
  
  .panel-content {
    padding: 30rpx;
  }
  
  .panel-title {
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 30rpx;
    text-align: center;
  }
  
  /* 颜色选择器 */
  .color-picker {
    display: flex;
    justify-content: space-around;
    margin-bottom: 30rpx;
  }
  
  .color-item {
    width: 80rpx;
    height: 80rpx;
    border-radius: 50%;
    border: 4rpx solid transparent;
  }
  
  .color-item.active {
    border-color: #333;
    box-shadow: 0 0 0 4rpx rgba(0, 0, 0, 0.1);
  }
  
  /* 重命名输入框 */
  .rename-input {
    border: 1rpx solid #eee;
    border-radius: 8rpx;
    padding: 20rpx;
    font-size: 28rpx;
    margin-bottom: 30rpx;
  }
  
  .confirm-btn {
    background-color: #4B8BF5;
    color: white;
    border-radius: 40rpx;
    font-size: 28rpx;
    padding: 16rpx 0;
  }

/* 滑块容器 */
.slider-container {
    margin-bottom: 30rpx;
  }
  
  .slider-label {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
  }
  
  /* 开关容器 */
  .switch-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
  }
  
  .switch-label {
    font-size: 28rpx;
    color: #333;
  }
  
  /* 底部工具栏 */
  .bottom-toolbar {
    display: flex;
    justify-content: space-around;
    background-color: white;
    border-top: 1rpx solid #eee;
    padding: 20rpx 0;
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 50;
  }
  
  .toolbar-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10rpx 0;
  }
  
  .toolbar-icon {
    width: 48rpx;
    height: 48rpx;
    margin-bottom: 8rpx;
  }
  
  .toolbar-text {
    font-size: 24rpx;
    color: #666;
  }
  
  .toolbar-item.active .toolbar-text {
    color: #4B8BF5;
  }

/* 图标样式 */
.cover-icon {
    width: 48rpx;
    height: 48rpx;
    background-color: #666;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-4.86 8.86l-3 3.87L9 13.14 6 17h12l-3.86-5.14z'/%3E%3C/svg%3E");
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 5v14H5V5h14m0-2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-4.86 8.86l-3 3.87L9 13.14 6 17h12l-3.86-5.14z'/%3E%3C/svg%3E");
  }
  
  .theme-icon {
    width: 48rpx;
    height: 48rpx;
    background-color: #666;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z'/%3E%3C/svg%3E");
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 3c-4.97 0-9 4.03-9 9s4.03 9 9 9c.83 0 1.5-.67 1.5-1.5 0-.39-.15-.74-.39-1.01-.23-.26-.38-.61-.38-.99 0-.83.67-1.5 1.5-1.5H16c2.76 0 5-2.24 5-5 0-4.42-4.03-8-9-8zm-5.5 9c-.83 0-1.5-.67-1.5-1.5S5.67 9 6.5 9 8 9.67 8 10.5 7.33 12 6.5 12zm3-4C8.67 8 8 7.33 8 6.5S8.67 5 9.5 5s1.5.67 1.5 1.5S10.33 8 9.5 8zm5 0c-.83 0-1.5-.67-1.5-1.5S13.67 5 14.5 5s1.5.67 1.5 1.5S15.33 8 14.5 8zm3 4c-.83 0-1.5-.67-1.5-1.5S16.67 9 17.5 9s1.5.67 1.5 1.5-.67 1.5-1.5 1.5z'/%3E%3C/svg%3E");
  }
  
  .rename-icon {
    width: 48rpx;
    height: 48rpx;
    background-color: #666;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3 17.25V21h3.75L17.81 9.94l-3.75-3.75L3 17.25zM20.71 7.04c.39-.39.39-1.02 0-1.41l-2.34-2.34c-.39-.39-1.02-.39-1.41 0l-1.83 1.83 3.75 3.75 1.83-1.83z'/%3E%3C/svg%3E");
  }
  
  .spacing-icon {
    width: 48rpx;
    height: 48rpx;
    background-color: #666;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3 21h18v-2H3v2zM3 8v8h18V8H3z'/%3E%3C/svg%3E");
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M3 21h18v-2H3v2zM3 8v8h18V8H3z'/%3E%3C/svg%3E");
  }
  
  .download-icon {
    width: 48rpx;
    height: 48rpx;
    background-color: #666;
    mask-size: contain;
    mask-repeat: no-repeat;
    mask-position: center;
    -webkit-mask-size: contain;
    -webkit-mask-repeat: no-repeat;
    -webkit-mask-position: center;
    mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E");
    -webkit-mask-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z'/%3E%3C/svg%3E");
  }
  
  .toolbar-item.active .toolbar-icon {
    background-color: #4B8BF5;
  }