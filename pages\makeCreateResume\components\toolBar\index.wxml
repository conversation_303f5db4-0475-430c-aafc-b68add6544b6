<view class="tool-bar">
  <view class="tool-item" bindtap="onCoverToggle">
    <text>开启封面</text>
  </view>
  
  <view class="tool-item" bindtap="onThemeClick">
    <text>主题色</text>
  </view>
  
  <view class="tool-item" bindtap="onRename">
    <text>重命名</text>
  </view>
  
  <view class="tool-item" bindtap="onSpacingClick">
    <text>字号间距</text>
  </view>

  <view class="tool-item" bindtap="onDownload">
    <text>下载文档</text>
  </view>
</view>

<!-- 主题色选择弹窗 -->
<view class="theme-popup" wx:if="{{showThemePopup}}">
  <view class="popup-mask" bindtap="onThemeClose"></view>
  <view class="popup-content">
    <view class="color-list">
      <view class="color-item" wx:for="{{colors}}" wx:key="index" bindtap="onThemeSelect" data-color="{{item}}">
        <view class="color-circle" style="background: {{item}}"></view>
        <text class="color-name">{{colorNames[index]}}</text>
      </view>
    </view>
  </view>
</view>

<!-- 间距设置弹窗 -->
<view class="spacing-popup" wx:if="{{showSpacingPopup}}">
  <view class="popup-mask" bindtap="onSpacingClose"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>字号间距设置</text>
      <view class="close" bindtap="onSpacingClose">×</view>
    </view>
    <view class="popup-body">
      <view class="spacing-item">
        <text>字号大小</text>
        <slider 
          min="8" 
          max="13" 
          step="1" 
          value="{{currentFontSize}}"
          block-size="20"
          show-value
          bindchange="onFontSizeChange"
        />
      </view>
      <view class="spacing-item">
        <text>行间距</text>
        <slider 
          min="1" 
          max="1.5" 
          step="0.1" 
          value="{{currentSpacing}}"
          block-size="20"
          show-value
          bindchange="onSpacingChange"
        />
      </view>
    </view>
  </view>
</view>

<!-- 重命名弹窗 -->
<view class="rename-popup" wx:if="{{showRenamePopup}}">
  <view class="popup-mask" bindtap="onRenameClose"></view>
  <view class="popup-content">
    <view class="popup-header">
      <text>重命名简历</text>
      <view class="close" bindtap="onRenameClose">×</view>
    </view>
    <view class="popup-body">
      <input 
        class="rename-input" 
        placeholder="请输入简历名称" 
        value="{{resumeName}}"
        bindinput="onRenameInput"
      />
      <view class="btn-group">
        <button class="btn cancel" bindtap="onRenameClose">取消</button>
        <button class="btn confirm" bindtap="onRenameConfirm">确定</button>
      </view>
    </view>
  </view>
</view> 