{"appid": "wx81f4f68d6911f5bd", "projectname": "test-program", "compileType": "miniprogram", "libVersion": "2.25.3", "packOptions": {"ignore": [], "include": []}, "setting": {"coverView": true, "es6": true, "postcss": true, "minified": true, "enhance": true, "showShadowRootInWxmlPanel": true, "packNpmRelationList": [], "compileHotReLoad": true, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}}, "condition": {}, "editorSetting": {"tabIndent": "insertSpaces", "tabSize": 2}}