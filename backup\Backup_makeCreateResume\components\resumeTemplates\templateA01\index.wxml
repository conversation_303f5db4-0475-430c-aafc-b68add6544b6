<wxs module="wxs">
function removeHtmlTags(str) {
  if (!str) return '';
  return str.replace(getRegExp('<[^>]+>', 'g'), '');
}

module.exports = {
  removeHtmlTags: removeHtmlTags
};
</wxs>

<view class="resumeTemplateA01" style="{{customStyle}}">
  <!-- 主标题 -->
  <view class="resume-header">
    <view class="resume-title">个人简历</view>
    <view class="icon-group">
      <image class="icon-circle" src="../images/icons/pencil.svg" />
      <image class="icon-circle" src="../images/icons/graduation-cap.svg" />
      <image class="icon-circle" src="../images/icons/briefcase.svg" />
    </view>
  </view>

  <!-- 提醒图标 -->
  <view class="tixing-wrapper">
    <view class="tixing-container1">
      <view class="tixing-icon"></view>
    </view>
    <view class="tixing-container2">
      <image class="tixing2-image" src="./images/tiXing2.svg" mode="aspectFit"></image>
    </view>
  </view>

  <!-- 使用sortedModules来控制模块显示顺序 -->
  <block wx:for="{{sortedModules}}" wx:key="order">
    <!-- 基本信息模块 -->
    <block wx:if="{{item.type === 'basicInfo' && item.data}}">
      <view class="section">
        <view class="title-wrapper" wx:if="{{item.data.title}}">
          <view class="title-bg"></view>
          <view class="title">{{item.data.title}}</view>
        </view>
        <view class="basic-info">
          <view class="info-content">
            <view class="info-grid">
              <block wx:if="{{item.data.name}}">
                <text class="info-item">姓　　名：{{item.data.name}}</text>
              </block>
              <block wx:if="{{item.data.birthday}}">
                <text class="info-item">生　　日：{{item.data.birthday}}</text>
              </block>
              <block wx:if="{{item.data.phone}}">
                <text class="info-item">电　　话：{{item.data.phone}}</text>
              </block>
              <block wx:if="{{item.data.marriage}}">
                <text class="info-item">婚　　姻：{{item.data.marriage}}</text>
              </block>
              <block wx:if="{{item.data.email}}">
                <text class="info-item">邮　　箱：{{item.data.email}}</text>
              </block>
              <block wx:if="{{item.data.politics}}">
                <text class="info-item">政治面貌：{{item.data.politics}}</text>
              </block>
              <block wx:if="{{item.data.city}}">
                <text class="info-item">城　　市：{{item.data.city}}</text>
              </block>
              <block wx:if="{{item.data.nation}}">
                <text class="info-item">民　　族：{{item.data.nation}}</text>
              </block>
              <block wx:if="{{item.data.age}}">
                <text class="info-item">年　　龄：{{item.data.age}}岁</text>
              </block>
              <block wx:if="{{item.data.hometown}}">
                <text class="info-item">籍　　贯：{{item.data.hometown}}</text>
              </block>
              <block wx:if="{{item.data.gender}}">
                <text class="info-item">性　　别：{{item.data.gender}}</text>
              </block>
              <block wx:if="{{item.data.height}}">
                <text class="info-item">身　　高：{{item.data.height}}cm</text>
              </block>
              <block wx:if="{{item.data.wechat}}">
                <text class="info-item">微　　信：{{item.data.wechat}}</text>
              </block>
              <block wx:if="{{item.data.weight}}">
                <text class="info-item">体　　重：{{item.data.weight}}kg</text>
              </block>
              <block wx:if="{{item.data.customContent1}}">
                <text class="info-item">{{item.data.customTitle1 || '自定义标题1'}}：{{item.data.customContent1}}</text>
              </block>
              <block wx:if="{{item.data.customContent2}}">
                <text class="info-item">{{item.data.customTitle2 || '自定义标题2'}}：{{item.data.customContent2}}</text>
              </block>
            </view>
          </view>
          <image 
            wx:if="{{item.data.photoUrl}}" 
            class="avatar" 
            src="{{item.data.photoUrl}}"
            mode="aspectFill"
          />
        </view>
      </view>
    </block>

    <!-- 求职意向模块 -->
    <block wx:if="{{item.type === 'jobIntention' && item.data}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">求职意向</view>
        </view>
        <view class="content job-intention-content">
          <view class="job-intention-item" wx:if="{{item.data.position}}">期望职位：{{item.data.position}}</view>
          <view class="job-intention-item" wx:if="{{item.data.salary}}">期望薪资：{{item.data.salary}}</view>
          <view class="job-intention-item" wx:if="{{item.data.city}}">期望城市：{{item.data.city}}</view>
          <view class="job-intention-item" wx:if="{{item.data.status}}">求职状态：{{item.data.status}}</view>
        </view>
      </view>
    </block>

    <!-- 在校经历模块 -->
    <block wx:if="{{item.type === 'school' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">在校经历</view>
        </view>
        <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="schoolItem">
          <view class="school-experience-header">
            <view class="activity">{{schoolItem.role}}</view>
            <view class="time" wx:if="{{schoolItem.startDate}}">{{schoolItem.startDate}} - {{schoolItem.endDate}}</view>
          </view>
          <view wx:if="{{schoolItem.content}}" class="description">
            <text>{{wxs.removeHtmlTags(schoolItem.content)}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 教育经历模块 -->
    <block wx:if="{{item.type === 'education' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">教育经历</view>
        </view>
        <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="eduItem">
          <view class="education-header">
            <view class="school">{{eduItem.school}}</view>
            <view class="major-degree">{{eduItem.major}}{{eduItem.degree ? '/' + eduItem.degree : ''}}</view>
            <view class="edu-date">{{eduItem.startDate}} - {{eduItem.endDate}}</view>
          </view>
          <view class="edu-description">
            <text>{{eduItem.courses ? '主修课程：' + eduItem.courses + '\n' : ''}}{{eduItem.description ? wxs.removeHtmlTags(eduItem.description) : ''}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 实习经历模块 -->
    <block wx:if="{{item.type === 'internship' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">实习经历</view>
        </view>
        <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="internItem">
          <view class="internship-header">
            <view class="company">{{internItem.company}}</view>
            <view class="company" wx:if="{{internItem.position}}">{{internItem.position}}</view>
            <view class="time" wx:if="{{internItem.startDate}}">{{internItem.startDate}} - {{internItem.endDate}}</view>
          </view>
          <view wx:if="{{internItem.content}}" class="description">
            <text>{{wxs.removeHtmlTags(internItem.content)}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 工作经历模块 -->
    <block wx:if="{{item.type === 'work' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">工作经历</view>
        </view>
        <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="workItem">
          <view class="internship-header">
            <view class="company">{{workItem.company}}</view>
            <view class="company" wx:if="{{workItem.position}}">{{workItem.position}}</view>
            <view class="time" wx:if="{{workItem.startDate}}">{{workItem.startDate}} - {{workItem.endDate}}</view>
          </view>
          <view wx:if="{{workItem.content}}" class="description">
            <text>{{wxs.removeHtmlTags(workItem.content)}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 项目经历模块 -->
    <block wx:if="{{item.type === 'project' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">项目经历</view>
        </view>
        <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="projectItem">
          <view class="internship-header">
            <view class="company">{{projectItem.projectName}}</view>
            <view class="company" wx:if="{{projectItem.role}}">{{projectItem.role}}</view>
            <view class="time" wx:if="{{projectItem.startDate}}">{{projectItem.startDate}} - {{projectItem.endDate}}</view>
          </view>
          <view wx:if="{{projectItem.description}}" class="description">
            <text>{{projectItem.description}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 技能特长模块 -->
    <block wx:if="{{item.type === 'skills' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">技能特长</view>
        </view>
        <view class="content">
          <view wx:for="{{item.data}}" wx:key="index" wx:for-item="skillItem" class="skill">
            <text>{{skillItem}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 获奖证书模块 -->
    <block wx:if="{{item.type === 'awards' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">获奖证书</view>
        </view>
        <view class="content">
          <view wx:for="{{item.data}}" wx:key="index" wx:for-item="awardItem" class="award">
            <text>{{awardItem}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 兴趣爱好模块 -->
    <block wx:if="{{item.type === 'interests' && item.data.length > 0}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">兴趣爱好</view>
        </view>
        <view class="content">
          <view wx:for="{{item.data}}" wx:key="index" wx:for-item="interestItem" class="interest">
            <text>{{interestItem}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 自我评价模块 -->
    <block wx:if="{{item.type === 'evaluation' && item.data[0].content}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">自我评价</view>
        </view>
        <view class="content">
          <view class="description">
            <text>{{item.data[0].content}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 自定义模块1 -->
    <block wx:if="{{item.type === 'custom1' && item.data}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">{{item.data.title}}</view>
        </view>
        <view class="content">
          <view class="custom-header">
            <view class="custom-name">{{item.data.role}}</view>
            <view class="time" wx:if="{{item.data.startDate}}">{{item.data.startDate}} - {{item.data.endDate}}</view>
          </view>
          <view class="description">
            <text>{{item.data.content}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 自定义模块2 -->
    <block wx:if="{{item.type === 'custom2' && item.data}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">{{item.data.title}}</view>
        </view>
        <view class="content">
          <view class="custom-header">
            <view class="custom-name">{{item.data.role}}</view>
            <view class="time" wx:if="{{item.data.startDate}}">{{item.data.startDate}} - {{item.data.endDate}}</view>
          </view>
          <view class="description">
            <text>{{item.data.content}}</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 自定义模块3 -->
    <block wx:if="{{item.type === 'custom3' && item.data}}">
      <view class="section">
        <view class="title-wrapper">
          <view class="title-bg"></view>
          <view class="title">{{item.data.title}}</view>
        </view>
        <view class="content">
          <view class="custom-header">
            <view class="custom-name">{{item.data.role}}</view>
            <view class="time" wx:if="{{item.data.startDate}}">{{item.data.startDate}} - {{item.data.endDate}}</view>
          </view>
          <view class="description">
            <text>{{item.data.content}}</text>
          </view>
        </view>
      </view>
    </block>
  </block>
</view> 