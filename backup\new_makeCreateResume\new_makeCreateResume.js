// pages/new_makeCreateResume/new_makeCreateResume.js
Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 简历数据
    resumeData: {},
    
    // 模板相关
    templates: [
      { id: 'templateA01', name: '模板一', thumbnail: '/pages/new_makeCreateResume/components/resumeTemplates/images/thumbnails/templateA01.png' },
      { id: 'templateA02', name: '模板二', thumbnail: '/pages/new_makeCreateResume/components/resumeTemplates/images/thumbnails/templateA02.png' },
      { id: 'templateA03', name: '模板三', thumbnail: '/pages/new_makeCreateResume/components/resumeTemplates/images/thumbnails/templateA03.png' }
    ],
    currentTemplateId: 'templateA01',
    
    // 样式配置
    styleConfig: {
      themeColor: '#2B6CB0',
      fontSize: '14px',
      letterSpacing: 'normal',
      resumeName: '个人简历'
    },
    
    // 渲染结果
    renderedHtml: '',
    
    // UI状态
    loading: false,
    showStylePanel: false,
    colorPickerVisible: false,
    
    // 底部操作栏选项
    bottomOptions: [
      { id: 'cover', name: '开启封面', icon: 'cover-icon' },
      { id: 'theme', name: '主题色', icon: 'theme-icon' },
      { id: 'rename', name: '重命名', icon: 'rename-icon' },
      { id: 'spacing', name: '字号间距', icon: 'spacing-icon' },
      { id: 'download', name: '下载文档', icon: 'download-icon' }
    ],
    currentOptionId: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 检查是否有传递的简历数据
    if (options.resumeData) {
      try {
        // 解析传递的简历数据
        const resumeData = JSON.parse(decodeURIComponent(options.resumeData));
        this.setData({ resumeData });
        console.log('从makeResume页面接收到的数据:', resumeData);
      } catch (error) {
        console.error('解析简历数据失败:', error);
        // 如果解析失败，从本地存储加载
        this.loadResumeData();
      }
    } else {
      // 如果没有传递数据，从本地存储加载
      this.loadResumeData();
    }
    
    // 初始化渲染第一个模板
    this.renderResume();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 加载简历数据
  loadResumeData() {
    // 如果已经有从makeResume页面传递过来的数据，则不需要再从本地存储加载
    if (this.data.resumeData && Object.keys(this.data.resumeData).length > 0) {
      return;
    }
    
    // 从各个存储键中获取数据并组合
    const basicInfo = wx.getStorageSync('basicInfo') || {};
    const education = wx.getStorageSync('education') || [];
    const workExperience = wx.getStorageSync('workExperience') || [];
    const projects = wx.getStorageSync('projects') || [];
    const skills = wx.getStorageSync('skills') || [];
    const awards = wx.getStorageSync('awardsList') || [];
    const evaluation = wx.getStorageSync('evaluation') || [];
    const interests = wx.getStorageSync('interests') || [];
    const custom1 = wx.getStorageSync('customContent1') || {};
    const custom2 = wx.getStorageSync('customContent2') || {};
    const custom3 = wx.getStorageSync('customContent3') || {};
    
    // 组合成完整的简历数据
    const resumeData = {
      basicInfo,
      education,
      workExperience,
      projects,
      skills,
      awards,
      evaluation,
      interests,
      custom1,
      custom2,
      custom3
    };
    
    this.setData({ resumeData });
  },
  
  // 选择模板
  selectTemplate(e) {
    const templateId = e.currentTarget.dataset.id;
    this.setData({ 
      currentTemplateId: templateId,
      loading: true
    });
    
    // 渲染新选择的模板
    this.renderResume();
  },
  
  // 渲染简历
  renderResume() {
    const { resumeData, currentTemplateId, styleConfig } = this.data;
    
    // 显示加载状态
    this.setData({ loading: true });
    
    // 准备请求参数
    // 检查resumeData的格式，适配从makeResume页面传递过来的数据格式
    let formattedResumeData = resumeData;
    
    // 如果是从makeResume页面传递过来的数据，需要进行格式转换
    if (resumeData.basicInfo && typeof resumeData.basicInfo.moduleOrder !== 'undefined') {
      // 已经是makeResume页面传递过来的格式，不需要转换
      console.log('使用makeResume页面传递的数据格式');
    } else {
      // 需要转换为服务器期望的格式
      console.log('转换本地存储的数据格式');
      formattedResumeData = {
        basicInfo: resumeData.basicInfo || {},
        education: resumeData.education || [],
        work: resumeData.workExperience || [],
        project: resumeData.projects || [],
        skills: resumeData.skills || [],
        awards: resumeData.awards || [],
        evaluation: resumeData.evaluation || [],
        interests: resumeData.interests || [],
        custom: {
          custom1: resumeData.custom1 || {},
          custom2: resumeData.custom2 || {},
          custom3: resumeData.custom3 || {}
        }
      };
    }
    
    const requestData = {
      resumeData: formattedResumeData,
      templateId: currentTemplateId,
      styleConfig
    };
    
    console.log('发送到服务器的数据:', requestData);
    
    
    // 发送请求到服务器渲染简历
    wx.request({
      url: 'https://gbw8848.cn/resume/preview',
      method: 'POST',
      data: requestData,
      success: (res) => {
        if (res.statusCode === 200 && res.data.html) {
          this.setData({
            renderedHtml: res.data.html,
            loading: false
          });
        } else {
          this.handleRenderError();
        }
      },
      fail: () => {
        this.handleRenderError();
      }
    });
  },
  
  // 处理渲染错误
  handleRenderError() {
    this.setData({ loading: false });
    wx.showToast({
      title: '渲染失败，请重试',
      icon: 'none'
    });
  },
  
  // 切换底部选项面板
  toggleOption(e) {
    const optionId = e.currentTarget.dataset.id;
    
    if (optionId === this.data.currentOptionId) {
      // 如果点击的是当前选中的选项，则关闭面板
      this.setData({
        showStylePanel: false,
        currentOptionId: '',
        colorPickerVisible: false
      });
    } else {
      // 否则，打开对应的面板
      this.setData({
        showStylePanel: true,
        currentOptionId: optionId,
        colorPickerVisible: optionId === 'theme'
      });
    }
  },
  
  // 更新主题色
  updateThemeColor(e) {
    const color = e.detail.color;
    this.setData({
      'styleConfig.themeColor': color
    });
    
    // 重新渲染简历
    this.renderResume();
  },
  
  // 更新字体大小
  updateFontSize(e) {
    const fontSize = e.detail.value;
    this.setData({
      'styleConfig.fontSize': fontSize + 'px'
    });
    
    // 重新渲染简历
    this.renderResume();
  },
  
  // 更新字符间距
  updateLetterSpacing(e) {
    const spacing = e.detail.value;
    this.setData({
      'styleConfig.letterSpacing': spacing + 'px'
    });
    
    // 重新渲染简历
    this.renderResume();
  },
  
  // 重命名简历
  renameResume(e) {
    const name = e.detail.value;
    this.setData({
      'styleConfig.resumeName': name
    });
  },
  
  // 切换封面
  toggleCover(e) {
    const enabled = e.detail.value;
    this.setData({
      'styleConfig.enableCover': enabled
    });
    
    // 重新渲染简历
    this.renderResume();
  },
  
  // 下载文档
  downloadDocument() {
    wx.showLoading({
      title: '准备下载...',
    });
    
    const { resumeData, currentTemplateId, styleConfig } = this.data;
    
    // 准备请求参数
    const requestData = {
      resumeData,
      templateId: currentTemplateId,
      styleConfig,
      format: 'pdf' // 或者 'docx'
    };
    
    // 发送请求到服务器生成文档
    wx.request({
      url: 'https://your-server-api/generate-document',
      method: 'POST',
      data: requestData,
      success: (res) => {
        wx.hideLoading();
        
        if (res.statusCode === 200 && res.data.downloadUrl) {
          // 打开文档或提供下载链接
          wx.showModal({
            title: '文档已生成',
            content: '您可以点击确定下载文档',
            success: (result) => {
              if (result.confirm) {
                wx.downloadFile({
                  url: res.data.downloadUrl,
                  success: (downloadRes) => {
                    if (downloadRes.statusCode === 200) {
                      wx.openDocument({
                        filePath: downloadRes.tempFilePath,
                        showMenu: true,
                        success: () => {
                          console.log('打开文档成功');
                        }
                      });
                    }
                  }
                });
              }
            }
          });
        } else {
          wx.showToast({
            title: '生成文档失败',
            icon: 'none'
          });
        }
      },
      fail: () => {
        wx.hideLoading();
        wx.showToast({
          title: '生成文档失败',
          icon: 'none'
        });
      }
    });
  }
})