<view class="container">
  <view class="formGroup">
    <!-- 学校名称 -->
    <view class="formItem">
      <text class="label">学校名称</text>
      <input class="input" placeholder="请输入学校名称" value="{{educationEditFormData.school}}" data-field="school" bindinput="handleInput"/>
    </view>

    <!-- 学历 -->
    <view class="formItem">
      <text class="label">学历</text>
      <picker range="{{degreeArray}}" value="{{degreeIndex}}" bindchange="handleDegreeChange">
        <input class="input" placeholder="请选择学历" disabled value="{{educationEditFormData.degree}}" />
      </picker>
    </view>

    <!-- 专业名称 -->
    <view class="formItem">
      <text class="label">专业名称</text>
      <input class="input" placeholder="请输入专业名称" value="{{educationEditFormData.major}}" data-field="major" bindinput="handleInput"/>
    </view>

    <!-- 学习时间 -->
    <view class="formItem">
      <text class="label">学习时间</text>
      <view class="datePicker">
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{educationEditFormData.startDate}}" data-field="startDate" bindchange="handleDateChange">
            <view class="picker {{educationEditFormData.startDate ? '' : 'placeholder'}}">
              {{educationEditFormData.startDate || '开始时间'}}
            </view>
          </picker>
        </view>
        <text class="separator">至</text>
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{educationEditFormData.endDate}}" data-field="endDate" bindchange="handleDateChange">
            <view class="picker {{educationEditFormData.endDate ? '' : 'placeholder'}}">
              {{educationEditFormData.endDate || '结束时间'}}
            </view>
          </picker>
        </view>
        <view class="nowBtn" bindtap="setEndDateToNow">至今</view>
      </view>
    </view>

    <!-- 专业课程 -->
    <view class="formItem">
      <text class="label">专业课程</text>
      <textarea class="textarea" placeholder="请输入主修课程（选填）" value="{{educationEditFormData.courses}}" data-field="courses" bindinput="handleInput"/>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="buttonGroup">
    <button class="saveBtn" bindtap="saveEducation">保存</button>
    <button wx:if="{{editIndex >= 0}}" class="deleteBtn" bindtap="deleteEducation">删除</button>
  </view>
</view> 