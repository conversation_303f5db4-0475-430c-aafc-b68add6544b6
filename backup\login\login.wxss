/* pages/user/login/login.wxss */
.container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  background-color: #f8f8f8;
  min-height: 100vh;
}

.header {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 60rpx;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.content {
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.login-tips {
  display: flex;
  flex-direction: column;
  margin-bottom: 40rpx;
}

.login-tips text {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 10rpx;
}

.login-btn {
  background-color: #4B8BF5;
  color: #fff;
  font-size: 32rpx;
  margin: 40rpx 0;
}

.skip-login {
  text-align: center;
  margin-top: 30rpx;
}

.skip-login text {
  font-size: 28rpx;
  color: #999;
  text-decoration: underline;
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.action-list {
  margin: 40rpx 0;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx 0;
  border-bottom: 1rpx solid #eee;
}

.action-item:last-child {
  border-bottom: none;
}

.action-item text {
  font-size: 30rpx;
  color: #333;
}

.arrow {
  color: #999;
}

.logout-btn {
  background-color: #f5f5f5;
  color: #ff4d4f;
  font-size: 32rpx;
  margin-top: 60rpx;
}
