/* pages/makeCreateResume/makeCreateResume.wxss */
page {
  background-color: #f5f5f5; /* 设置页面统一背景色为浅灰 */
  height: 100%;
}

.resume-page {
  /* height: 100vh; */
  display: flex;
  flex-direction: column;
  /* background: #F2F2F2; */ /* 移除这里的背景色，统一由 page 控制 */
}

.preview-area {
  flex: 1;
  overflow-y: auto;
  padding: 20rpx; /* 增加内边距，让预览居中感更强 */
  /* background: #F2F2F2; */ /* 移除这里的背景色 */
  box-sizing: border-box; /* 确保 padding 包含在内 */
}

/* .template-area { */
  /* height: 200rpx; */
  /* background: #5022cf; */
  /* padding: 20rpx; */
  /* border-top: 1rpx solid #eee; */
/* } */

.toolbar-area {
  height: 100rpx;
}

/* 隐藏HTML生成器 */
.hidden-generator {
  position: absolute;
  left: -9999px;
  visibility: hidden;
  pointer-events: none;
}

.container {
  /* min-height: 100vh; */
  /* background: #F2F2F2; */ /* 移除这里的背景色 */
  padding: 0; /* 移除 .container 的 padding，由 .preview-area 控制 */
  /* padding-bottom: 120rpx; */ /* 确保底部工具栏不遮挡内容，如果工具栏是fixed定位则不需要 */
  box-sizing: border-box;
}

/* 底部操作栏 */
.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx;
  background: white;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  z-index: 100;
}

.generate-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background: #4B8BF5;
  color: white;
  border-radius: 8rpx;
  font-size: 32rpx;
} 