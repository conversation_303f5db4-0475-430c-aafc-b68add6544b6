# 简历模板图片资源使用说明

## 目录结构
```
resumeTemplates/
├── images/                    # 公共图片资源
│   ├── icons/                # 图标类图片
│   └── backgrounds/          # 背景类图片
├── templateA01/
│   └── images/              # templateA01 专属图片
├── templateA02/
│   └── images/              # templateA02 专属图片
└── templateA03/
    └── images/              # templateA03 专属图片
```

## 使用规范

### 1. 图片命名规则
- 使用小写字母
- 单词之间使用连字符（-）分隔
- 命名要具有描述性
- 示例：`header-background.png`, `edit-icon.svg`

### 2. 图片格式
- 图标优先使用 SVG 格式
- 照片使用 JPG 格式
- 需要透明背景的图片使用 PNG 格式
- 所有图片都应进行适当压缩，平衡质量和文件大小

### 3. SVG 动态主题色处理方案
当需要 SVG 图标跟随主题色变化时，可以使用以下方案：

1. SVG 文件处理：
   ```svg
   <!-- 必须设置合适的width、height和viewBox -->
   <svg width="1000" height="50" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 50">
     <!-- 重要：所有可能影响颜色的属性都必须设置为white -->
     <g fill="white" stroke="white" fill-opacity="1" stroke-opacity="1">
       <!-- 路径示例 -->
       <path d="M10 10L90 90" />
       <!-- 矩形示例 -->
       <rect x="10" y="10" width="80" height="80" />
       <!-- 圆形示例 -->
       <circle cx="50" cy="50" r="40" />
       <!-- 线条示例 -->
       <line x1="10" y1="10" x2="90" y2="90" />
     </g>
   </svg>
   ```

2. SVG 转 Base64：
   ```javascript
   // 重要：SVG转Base64时必须进行以下处理
   // 1. 移除所有换行符
   // 2. 确保SVG标签闭合正确
   // 3. 确保所有属性值使用双引号
   // 4. 特殊字符需要转义
   const svgContent = `<svg ...></svg>`;  // 一行完整的SVG内容
   const base64 = btoa(svgContent);
   ```

3. WXML 实现：
   ```html
   <!-- 不使用image标签，改用view标签 -->
   <view class="icon"></view>
   ```

4. WXSS 实现：
   ```css
   .icon {
     /* 设置元素尺寸，这很重要 */
     width: 100rpx;  /* 根据实际需要设置 */
     height: 50rpx;  /* 根据实际需要设置 */
     /* 使用主题色作为背景 */
     background-color: var(--theme-color, #2B6CB0);
     /* 使用 SVG 作为遮罩，注意 center / contain 的设置 */
     /* 重要：在微信小程序中，mask-image的url必须是base64格式，且SVG内容需要是一行 */
     -webkit-mask: url("data:image/svg+xml;base64,PHN2Zy4uLj4...") no-repeat center / contain;
     mask: url("data:image/svg+xml;base64,PHN2Zy4uLj4...") no-repeat center / contain;
     /* 确保背景色完全填充 */
     background-size: 100% 100%;
     background-repeat: no-repeat;
   }
   ```

5. 微信小程序特殊处理：
   - SVG 内容必须是单行，不能有换行符
   - 所有属性值必须使用双引号
   - SVG 标签必须正确闭合
   - Base64 编码前需要移除所有空格和注释
   - 特殊字符需要正确转义
   - mask-image 的 url 必须是完整的 base64 格式
   - 建议使用在线工具验证 SVG 的有效性

6. 工作原理：
   - SVG 中所有颜色相关属性都设为白色，作为完整的遮罩模板
   - 背景色会通过遮罩的白色部分显示出来
   - 遮罩的白色部分决定了最终显示的形状
   - CSS变量更新时，背景色会自动更新
   - view标签的尺寸和遮罩的缩放方式决定最终显示效果

7. 颜色相关的SVG属性检查清单：
   - fill: 填充颜色
   - stroke: 描边颜色
   - fill-opacity: 填充透明度
   - stroke-opacity: 描边透明度
   - stop-color: 渐变色节点颜色
   - flood-color: 滤镜颜色
   - lighting-color: 光照颜色
   所有这些属性都应该设置为white或1（对于透明度）

8. 注意事项：
   - SVG 文件必须设置正确的 width、height 和 viewBox 属性
   - 检查 SVG 中所有可能影响颜色的属性
   - 确保所有路径和形状的颜色属性都设置为white
   - 透明度相关的属性应该设置为1
   - 必须将 SVG 转换为 base64 格式后嵌入 WXSS
   - 使用 view 标签而不是 image 标签
   - view 标签必须设置具体的宽高
   - background-color 和 mask 属性缺一不可

9. 常见问题：
   - 如果部分区域没有变色，检查：
     * SVG 中是否有未设置为white的颜色属性
     * 是否有透明度设置影响了显示
     * 是否有未包含在遮罩中的路径
   - 如果遮罩完全不显示，检查：
     * base64编码是否正确
     * view标签是否有正确的尺寸
     * mask属性是否正确设置
   - 如果显示不完整，检查：
     * viewBox设置是否合适
     * mask的缩放设置是否正确
     * view标签的尺寸是否合适

### 4. 图片引用方式
```javascript
// 引用公共图片
"../../images/icons/edit-icon.svg"
"../../images/backgrounds/header-bg.png"

// 引用模板专属图片
"./images/preview.png"
"./images/thumbnail.png"
```

### 5. 注意事项
- 新增模板时，请在对应模板目录下创建 images 文件夹
- 公共使用的图片资源放在根目录的 images 文件夹下
- 图片尺寸应保持一致性，特别是模板预览图和缩略图
- 及时清理未使用的图片资源
- 需要动态变色的 SVG 图标，使用遮罩方案处理
- Base64 转换后的代码较长，注意代码组织

### 6. HTML 生成时的处理
对于使用 mask-image 和 background-color 实现的动态颜色 SVG 图标，在生成 HTML 时需要特别处理：

1. CSS 转换：
   ```css
   /* 小程序中的原始样式 */
   .icon {
     background-color: var(--theme-color);
     -webkit-mask: url("data:image/svg+xml;base64,...");
   }

   /* 生成的 HTML 中的样式 */
   .icon {
     background-color: #specific-color; /* 使用具体的颜色值 */
     -webkit-mask: url("data:image/svg+xml;base64,...");
   }
   ```

2. 注意事项：
   - 确保 base64 编码的 SVG 数据被正确保留
   - CSS 变量需要被解析为具体的颜色值
   - 保持 mask 属性的浏览器兼容性（需要添加相应的前缀）
   - 可能需要添加降级方案以支持不同的浏览器

3. HTML 生成器中的处理：
   - 在生成 HTML 时捕获当前的主题色值
   - 将 CSS 变量替换为实际的颜色值
   - 保留完整的 mask-image 定义
   - 确保生成的 HTML 在不同浏览器中都能正确显示 