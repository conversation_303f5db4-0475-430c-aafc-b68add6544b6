<wxs module="wxs">
function removeHtmlTags(str) {
  if (!str) return '';
  return str.replace(getRegExp('<[^>]+>', 'g'), '');
}

module.exports = {
  removeHtmlTags: removeHtmlTags
};
</wxs>

<view class="resumeTemplateA02" style="{{customStyle}}">
  <!-- 主标题 -->
  <view class="resume-header">
    <view class="title-decoration"></view>
    <view class="header-content">
      <view class="title-images">
        <image class="resume-title-image" src="./images/chiLun.png" mode="heightFix"></image>
        <image class="resume-subtitle-image" src="./images/chiLun.png" mode="heightFix"></image>
        <image class="resume-third-image" src="./images/chiLun.png" mode="heightFix"></image>
        <image class="resume-fourth-image" src="./images/chiLun.png" mode="heightFix"></image>
      </view>
      <image class="personal-resume-icon" src="./images/personal_resume.png" mode="heightFix"></image>
    </view>
  </view>

  <!-- 新增两栏布局容器 -->
  <view class="resume-content">
    <!-- 左侧栏 - 基本信息 -->
    <view class="left-column">
      <block wx:for="{{sortedModules}}" wx:key="order">
        <block wx:if="{{item.type === 'basicInfo' && item.data}}">
          <view class="basic-section">
            <view class="basic-info">
              <view class="basic-info-bg"></view>
              <view class="basic-info-content">
                <image 
                  wx:if="{{item.data.photoUrl}}" 
                  class="avatar" 
                  src="{{item.data.photoUrl}}"
                  mode="aspectFill"
                ></image>
                <view class="info-content">
                  <view class="info-grid">
                    <block wx:if="{{item.data.name}}">
                      <view class="name-item name-item-dark">{{item.data.name}}</view>
                      <view class="contact-container">
                        <view class="contact-icon"></view>
                        <text class="contact-text">联系方式</text>
                      </view>
                      <block wx:if="{{item.data.phone}}">
                        <text class="info-item"><text class="label-text">电话</text>：{{item.data.phone}}</text>
                      </block>
                      <block wx:if="{{item.data.email}}">
                        <text class="info-item"><text class="label-text">邮箱</text>：{{item.data.email}}</text>
                      </block>
                      <view class="email-decoration"></view>
                      <view class="info-container">
                        <view class="info-icon"></view>
                        <text class="info-text">个人信息</text>
                      </view>
                    </block>
                    <block wx:if="{{item.data.birthday}}">
                      <text class="info-item"><text class="label-text">生日</text>：{{item.data.birthday}}</text>
                    </block>
                    <block wx:if="{{item.data.marriage}}">
                      <text class="info-item"><text class="label-text">婚姻</text>：{{item.data.marriage}}</text>
                    </block>
                    <block wx:if="{{item.data.politics}}">
                      <text class="info-item"><text class="label-text">政治面貌</text>：{{item.data.politics}}</text>
                    </block>
                    <block wx:if="{{item.data.city}}">
                      <text class="info-item"><text class="label-text">城市</text>：{{item.data.city}}</text>
                    </block>
                    <block wx:if="{{item.data.nation}}">
                      <text class="info-item"><text class="label-text">民族</text>：{{item.data.nation}}</text>
                    </block>
                    <block wx:if="{{item.data.age}}">
                      <text class="info-item"><text class="label-text">年龄</text>：{{item.data.age}}岁</text>
                    </block>
                    <block wx:if="{{item.data.hometown}}">
                      <text class="info-item"><text class="label-text">籍贯</text>：{{item.data.hometown}}</text>
                    </block>
                    <block wx:if="{{item.data.gender}}">
                      <text class="info-item"><text class="label-text">性别</text>：{{item.data.gender}}</text>
                    </block>
                    <block wx:if="{{item.data.height}}">
                      <text class="info-item"><text class="label-text">身高</text>：{{item.data.height}}cm</text>
                    </block>
                    <block wx:if="{{item.data.wechat}}">
                      <text class="info-item"><text class="label-text">微信</text>：{{item.data.wechat}}</text>
                    </block>
                    <block wx:if="{{item.data.weight}}">
                      <text class="info-item"><text class="label-text">体重</text>：{{item.data.weight}}kg</text>
                    </block>
                    <block wx:if="{{item.data.customContent1}}">
                      <text class="info-item"><text class="label-text">{{item.data.customTitle1 || '自定义标题1'}}</text>：{{item.data.customContent1}}</text>
                    </block>
                    <block wx:if="{{item.data.customContent2}}">
                      <text class="info-item"><text class="label-text">{{item.data.customTitle2 || '自定义标题2'}}</text>：{{item.data.customContent2}}</text>
                    </block>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>

    <!-- 右侧栏 - 其他模块 -->
    <view class="right-column">
      <block wx:for="{{sortedModules}}" wx:key="order">
        <!-- 求职意向模块 -->
        <block wx:if="{{item.type === 'jobIntention' && item.data}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="job-icon"></view>
              <view class="title">求职意向</view>
            </view>
            <view class="content job-intention-content">
              <view class="job-intention-item" wx:if="{{item.data.position}}">期望职位：{{item.data.position}}</view>
              <view class="job-intention-item" wx:if="{{item.data.salary}}">期望薪资：{{item.data.salary}}</view>
              <view class="job-intention-item" wx:if="{{item.data.city}}">期望城市：{{item.data.city}}</view>
              <view class="job-intention-item" wx:if="{{item.data.status}}">求职状态：{{item.data.status}}</view>
            </view>
          </view>
        </block>

        <!-- 在校经历模块 -->
        <block wx:if="{{item.type === 'school' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="school-icon"></view>
              <view class="title">在校经历</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="schoolItem">
              <view class="school-experience-header">
                <view class="activity">{{schoolItem.role}}</view>
                <view class="time" wx:if="{{schoolItem.startDate}}">{{schoolItem.startDate}} - {{schoolItem.endDate}}</view>
              </view>
              <view wx:if="{{schoolItem.content}}" class="description">
                <text>{{wxs.removeHtmlTags(schoolItem.content)}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 教育经历模块 -->
        <block wx:if="{{item.type === 'education' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="education-icon"></view>
              <view class="title">教育经历</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="eduItem">
              <view class="internship-header">
                <view class="company">{{eduItem.school}}</view>
                <view class="company" wx:if="{{eduItem.major}}">{{eduItem.major}}{{eduItem.degree ? '/' + eduItem.degree : ''}}</view>
                <view class="time" wx:if="{{eduItem.startDate}}">{{eduItem.startDate}} - {{eduItem.endDate}}</view>
              </view>
              <view wx:if="{{eduItem.courses}}" class="description">
                <text><text class="courses-label">主修课程：</text>{{eduItem.courses}}</text>
              </view>
              <view wx:if="{{eduItem.description}}" class="description">
                <text>{{eduItem.description}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 实习经历模块 -->
        <block wx:if="{{item.type === 'internship' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="internship-icon"></view>
              <view class="title">实习经历</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="internItem">
              <view class="internship-header">
                <view class="company">{{internItem.company}}</view>
                <view class="company" wx:if="{{internItem.position}}">{{internItem.position}}</view>
                <view class="time" wx:if="{{internItem.startDate}}">{{internItem.startDate}} - {{internItem.endDate}}</view>
              </view>
              <view wx:if="{{internItem.content}}" class="description">
                <text>{{wxs.removeHtmlTags(internItem.content)}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 工作经历模块 -->
        <block wx:if="{{item.type === 'work' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="work-icon"></view>
              <view class="title">工作经历</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="workItem">
              <view class="internship-header">
                <view class="company">{{workItem.company}}</view>
                <view class="company" wx:if="{{workItem.position}}">{{workItem.position}}</view>
                <view class="time" wx:if="{{workItem.startDate}}">{{workItem.startDate}} - {{workItem.endDate}}</view>
              </view>
              <view wx:if="{{workItem.content}}" class="description">
                <text>{{wxs.removeHtmlTags(workItem.content)}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 项目经历模块 -->
        <block wx:if="{{item.type === 'project' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="project-icon"></view>
              <view class="title">项目经历</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="projectItem">
              <view class="internship-header">
                <view class="company">{{projectItem.projectName}}</view>
                <view class="company" wx:if="{{projectItem.role}}">{{projectItem.role}}</view>
                <view class="time" wx:if="{{projectItem.startDate}}">{{projectItem.startDate}} - {{projectItem.endDate}}</view>
              </view>
              <view wx:if="{{projectItem.description}}" class="description">
                <text>{{projectItem.description}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 技能特长模块 -->
        <block wx:if="{{item.type === 'skills' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="skill-icon"></view>
              <view class="title">技能特长</view>
            </view>
            <view class="content">
              <view wx:for="{{item.data}}" wx:key="index" wx:for-item="skillItem" class="skill">
                <text>{{skillItem}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 获奖证书模块 -->
        <block wx:if="{{item.type === 'awards' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="award-icon"></view>
              <view class="title">获奖证书</view>
            </view>
            <view class="content">
              <view wx:for="{{item.data}}" wx:key="index" wx:for-item="awardItem" class="award">
                <text>{{awardItem}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 兴趣爱好模块 -->
        <block wx:if="{{item.type === 'interests' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="interest-icon"></view>
              <view class="title">兴趣爱好</view>
            </view>
            <view class="content">
              <view wx:for="{{item.data}}" wx:key="index" wx:for-item="interestItem" class="interest">
                <text>{{interestItem}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自我评价模块 -->
        <block wx:if="{{item.type === 'evaluation' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="evaluation-icon"></view>
              <view class="title">自我评价</view>
            </view>
            <view class="content">
              <view wx:for="{{item.data}}" wx:key="index" wx:for-item="evalItem" class="evaluation">
                <text>{{evalItem.content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自定义模块1 -->
        <block wx:if="{{item.type === 'custom1' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="custom-icon"></view>
              <view class="title">{{item.data[0].customName || '自定义模块一'}}</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="customItem">
              <view class="custom-header">
                <view class="custom-name">{{customItem.role || ''}}</view>
                <view class="time" wx:if="{{customItem.startDate}}">{{customItem.startDate}} - {{customItem.endDate}}</view>
              </view>
              <view wx:if="{{customItem.content}}" class="description">
                <text>{{customItem.content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自定义模块2 -->
        <block wx:if="{{item.type === 'custom2' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="custom-icon"></view>
              <view class="title">{{item.data[0].customName || '自定义模块二'}}</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="customItem">
              <view class="custom-header">
                <view class="custom-name">{{customItem.role || ''}}</view>
                <view class="time" wx:if="{{customItem.startDate}}">{{customItem.startDate}} - {{customItem.endDate}}</view>
              </view>
              <view wx:if="{{customItem.content}}" class="description">
                <text>{{customItem.content}}</text>
              </view>
            </view>
          </view>
        </block>

        <!-- 自定义模块3 -->
        <block wx:if="{{item.type === 'custom3' && item.data.length > 0}}">
          <view class="section">
            <view class="title-wrapper">
              <view class="title-bg"></view>
              <view class="custom-icon"></view>
              <view class="title">{{item.data[0].customName || '自定义模块三'}}</view>
            </view>
            <view class="content" wx:for="{{item.data}}" wx:key="index" wx:for-item="customItem">
              <view class="custom-header">
                <view class="custom-name">{{customItem.role || ''}}</view>
                <view class="time" wx:if="{{customItem.startDate}}">{{customItem.startDate}} - {{customItem.endDate}}</view>
              </view>
              <view wx:if="{{customItem.content}}" class="description">
                <text>{{customItem.content}}</text>
              </view>
            </view>
          </view>
        </block>
      </block>
    </view>
  </view>
</view> 


*/