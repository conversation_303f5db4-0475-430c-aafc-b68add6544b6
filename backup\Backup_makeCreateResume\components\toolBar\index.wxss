.tool-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 110rpx;
  background: #4080FF;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx;
  z-index: 90;
}

.tool-item {
  flex: 1;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.tool-item text {
  color: #FFFFFF;
  font-size: 28rpx;
}

/* 主题色弹窗 */
.theme-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
}

.popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
}

.popup-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #FFFFFF;
  border-radius: 24rpx 24rpx 0 0;
  padding: 30rpx;
}

.color-list {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 30rpx 50rpx;
  padding: 40rpx;
  width: 600rpx;
  margin: 0 auto;
}

.color-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
}

.color-circle {
  width: 120rpx;
  height: 80rpx;
  border-radius: 12rpx;
  border: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.color-circle.selected {
  border-color: #333;
  transform: scale(1.05);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.color-name {
  font-size: 24rpx;
  color: #333;
  text-align: center;
}

/* 间距设置弹窗 */
.spacing-popup {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
}

.spacing-popup .popup-content {
  padding: 40rpx;
}

.popup-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40rpx;
}

.popup-header text {
  font-size: 32rpx;
  font-weight: bold;
}

.close {
  font-size: 40rpx;
  color: #999;
}

.spacing-item {
  margin-bottom: 30rpx;
}

.spacing-item text {
  display: block;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: #333;
}

slider {
  margin: 0;
}

/* 重命名弹窗样式 */
.rename-popup {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.rename-popup .popup-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.rename-popup .popup-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 12rpx;
  overflow: hidden;
}

.rename-popup .popup-header {
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.rename-popup .popup-header .close {
  font-size: 40rpx;
  color: #999;
  padding: 0 10rpx;
}

.rename-popup .popup-body {
  padding: 30rpx;
}

.rename-popup .rename-input {
  width: 100%;
  height: 80rpx;
  border: 1rpx solid #ddd;
  border-radius: 8rpx;
  padding: 0 20rpx;
  margin-bottom: 30rpx;
  box-sizing: border-box;
}

.rename-popup .btn-group {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

.rename-popup .btn {
  padding: 0 40rpx;
  height: 70rpx;
  line-height: 70rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.rename-popup .btn.cancel {
  background: #f5f5f5;
  color: #666;
}

.rename-popup .btn.confirm {
  background: #2B6CB0;
  color: #fff;
} 