<view class="container1">
  <!-- 顶部标题已经在导航栏显示 -->

  <!-- 名称输入区域 -->
  <view class="formItem1">
    <view class="label1 required">自定义名称一</view>
    <input class="input1"
           placeholder="请输入"
           value="{{custom1FormData.customName}}"
           bindinput="handleNameInput"/>
  </view>

  <!-- 时间选择区域 -->
  <view class="formItem1">
    <view class="label1">时间</view>
    <view class="datePicker1">
      <picker mode="date" fields="month" value="{{custom1FormData.startDate}}" bindchange="handleStartDateChange">
        <view class="pickerText1">{{custom1FormData.startDate || '请选择(选填)'}}</view>
      </picker>
      <text class="separator1">—</text>
      <picker mode="date" fields="month" value="{{custom1FormData.endDate}}" bindchange="handleEndDateChange">
        <view class="pickerText1">{{custom1FormData.endDate || '请选择(选填)'}}</view>
      </picker>
      <view class="toNow1" bindtap="setToNow">至今</view>
    </view>
  </view>

  <!-- 角色输入区域 -->
  <view class="formItem1">
    <view class="label1">角色</view>
    <input class="input1"
           placeholder="请输入(选填)"
           value="{{custom1FormData.role}}"
           bindinput="handleRoleInput"/>
  </view>

  <!-- 自定义内容区域 -->
  <view class="formItem1 contentArea1">
    <view class="label1">自定义内容</view>
    <view class="editorToolbar1">
      <view class="toolItem1" bindtap="handleBold">
        <text class="{{customFormStyle.isBold ? 'active1' : ''}}">B</text>
      </view>
      <view class="toolItem1" bindtap="handleItalic">
        <text class="{{customFormStyle.isItalic ? 'active1' : ''}}">I</text>
      </view>
      <view class="toolItem1" bindtap="handleUnderline">
        <text class="{{customFormStyle.isUnderline ? 'active1' : ''}}">U</text>
      </view>
      <view class="toolItem1" bindtap="handleList">
        <text class="{{customFormStyle.isList ? 'active1' : ''}}">≡</text>
      </view>
    </view>
    <textarea class="contentInput1"
              placeholder="请输入自定义的详细内容"
              value="{{custom1FormData.content}}"
              bindinput="handleContentInput"
              maxlength="-1"
              auto-height/>

  </view>

  <!-- 底部按钮组 -->
  <view class="buttonGroup1">
    <button class="saveBtn1" bindtap="saveContent">保存信息</button>
    <button class="deleteBtn1" bindtap="deleteContent">删除</button>
  </view>
</view>