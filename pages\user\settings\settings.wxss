/* pages/user/settings/settings.wxss */
.container {
  display: flex;
  flex-direction: column;
  background-color: #f8f8f8;
  min-height: 100vh;
  padding-bottom: 40rpx;
}

/* 用户信息区域样式 */
.user-info-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx 0;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: 20rpx;
}

.nickname {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

/* 设置项区域样式 */
.settings-section, .account-section, .about-section {
  background-color: #fff;
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  color: #999;
  padding: 20rpx 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.setting-item:last-child {
  border-bottom: none;
}

.setting-info {
  display: flex;
  flex-direction: column;
}

.setting-label {
  font-size: 30rpx;
  color: #333;
  margin-bottom: 6rpx;
}

.setting-desc {
  font-size: 24rpx;
  color: #999;
}

/* 账号操作区域样式 */
.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.action-item:last-child {
  border-bottom: none;
}

.action-label {
  font-size: 30rpx;
  color: #333;
}

.logout-label {
  color: #ff4d4f;
}

.action-arrow {
  width: 16rpx;
  height: 16rpx;
  border-top: 3rpx solid #999;
  border-right: 3rpx solid #999;
  transform: rotate(45deg);
}

/* 版本信息样式 */
.version-info {
  text-align: center;
  padding: 40rpx 0;
}

.version-info text {
  font-size: 24rpx;
  color: #999;
}
