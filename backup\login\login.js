// pages/user/login/login.js
const userApi = require('../../../utils/api/userApi');

Page({
  data: {
    isLoading: false,
    redirectUrl: '', // 登录成功后的重定向URL
    loginStatus: 'checking' // checking, logged, not_logged
  },

  onLoad(options) {
    // 获取重定向URL
    if (options.redirect) {
      this.setData({
        redirectUrl: decodeURIComponent(options.redirect)
      });
    }

    // 检查是否已登录
    this.checkLoginStatus();
  },

  // 检查登录状态
  checkLoginStatus() {
    const token = wx.getStorageSync('userToken');
    const userId = wx.getStorageSync('userId');

    if (token && userId) {
      this.setData({
        loginStatus: 'logged'
      });

      // 如果已登录且有重定向URL，则跳转
      if (this.data.redirectUrl) {
        wx.navigateTo({
          url: this.data.redirectUrl
        });
      } else {
        // 否则返回上一页
        wx.navigateBack();
      }
    } else {
      this.setData({
        loginStatus: 'not_logged'
      });
    }
  },

  // 执行微信登录
  performLogin() {
    this.setData({ isLoading: true });

    // 获取登录凭证
    wx.login({
      success: (loginRes) => {
        if (loginRes.code) {
          console.log('获取微信登录code成功:', loginRes.code);

          // 使用API模块发送登录请求
          userApi.login(loginRes.code)
            .then((res) => {
              console.log('手动登录成功');

              // 保存登录信息
              const { token, userId, membershipInfo } = res;
              wx.setStorageSync('userToken', token);
              wx.setStorageSync('userId', userId);

              // 保存会员信息
              if (membershipInfo) {
                wx.setStorageSync('membershipInfo', membershipInfo);
              }

              // 更新全局状态
              const app = getApp();
              if (app) {
                app.globalData.userToken = token;
                app.globalData.userId = userId;
                app.globalData.hasUserInfo = true;
                if (membershipInfo) {
                  app.globalData.isMember = membershipInfo.isMember || false;
                  app.globalData.membershipExpiry = membershipInfo.expiry || null;
                }
              }

              // 更新状态
              this.setData({
                loginStatus: 'logged',
                isLoading: false
              });

              // 记录登录行为
              this.recordUserAction('manual_login');

              // 登录成功提示
              wx.showToast({
                title: '登录成功',
                icon: 'success'
              });

              // 如果有重定向URL，则跳转
              if (this.data.redirectUrl) {
                setTimeout(() => {
                  wx.navigateTo({
                    url: this.data.redirectUrl
                  });
                }, 1500);
              } else {
                // 否则返回上一页
                setTimeout(() => {
                  wx.navigateBack();
                }, 1500);
              }
            })
            .catch((err) => {
              console.error('手动登录失败:', err);
              this.setData({ isLoading: false });
            });
        } else {
          console.error('获取登录凭证失败:', loginRes);
          wx.showToast({
            title: '登录失败，请重试',
            icon: 'none'
          });
          this.setData({ isLoading: false });
        }
      },
      fail: (err) => {
        console.error('wx.login调用失败:', err);
        wx.showToast({
          title: '登录失败，请重试',
          icon: 'none'
        });
        this.setData({ isLoading: false });
      }
    });
  },



  // 退出登录
  logout() {
    wx.showModal({
      title: '提示',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          // 记录退出登录行为
          this.recordUserAction('logout');

          // 清除本地存储的登录信息
          wx.removeStorageSync('userToken');
          wx.removeStorageSync('userId');
          wx.removeStorageSync('membershipInfo');

          // 更新状态
          this.setData({
            loginStatus: 'not_logged'
          });

          // 更新全局状态
          const app = getApp();
          if (app) {
            app.globalData.userToken = null;
            app.globalData.userId = null;
            app.globalData.hasUserInfo = false;
            app.globalData.isMember = false;
            app.globalData.membershipExpiry = null;
          }

          wx.showToast({
            title: '已退出登录',
            icon: 'success'
          });
        }
      }
    });
  }
})
