<!--pages/new_makeCreateResume/new_makeCreateResume.wxml-->
<view class="resume-preview-page">
  <!-- 顶部导航 -->
  <view class="nav-header">
    <view class="nav-title">个人简历预览</view>
  </view>
  
  <!-- 简历预览区域 -->
  <view class="resume-preview-container">
    <view class="resume-preview-wrapper">
      <!-- 加载状态 -->
      <view class="loading-mask" wx:if="{{loading}}">
        <view class="loading-spinner"></view>
      </view>
      
      <!-- 简历内容 -->
      <rich-text class="resume-content" nodes="{{renderedHtml}}"></rich-text>
    </view>
  </view>
  
  <!-- 模板选择区域 -->
  <view class="template-selector">
    <scroll-view scroll-x="true" class="template-scroll">
      <view 
        class="template-item {{currentTemplateId === template.id ? 'active' : ''}}" 
        wx:for="{{templates}}" 
        wx:key="id" 
        wx:for-item="template"
        data-id="{{template.id}}"
        bindtap="selectTemplate"
      >
        <image class="template-thumbnail" src="{{template.thumbnail}}" mode="aspectFill"></image>
        <view class="template-name">{{template.name}}</view>
      </view>
    </scroll-view>
  </view>
  
  <!-- 样式设置面板 -->
  <view class="style-panel {{showStylePanel ? 'show' : ''}}">
    <!-- 主题色设置 -->
    <view class="panel-content" wx:if="{{currentOptionId === 'theme'}}">
      <view class="panel-title">选择主题色</view>
      <view class="color-picker">
        <view 
          class="color-item {{styleConfig.themeColor === '#2B6CB0' ? 'active' : ''}}" 
          style="background-color: #2B6CB0;"
          data-color="#2B6CB0"
          bindtap="updateThemeColor"
        ></view>
        <view 
          class="color-item {{styleConfig.themeColor === '#38A169' ? 'active' : ''}}" 
          style="background-color: #38A169;"
          data-color="#38A169"
          bindtap="updateThemeColor"
        ></view>
        <view 
          class="color-item {{styleConfig.themeColor === '#E53E3E' ? 'active' : ''}}" 
          style="background-color: #E53E3E;"
          data-color="#E53E3E"
          bindtap="updateThemeColor"
        ></view>
        <view 
          class="color-item {{styleConfig.themeColor === '#805AD5' ? 'active' : ''}}" 
          style="background-color: #805AD5;"
          data-color="#805AD5"
          bindtap="updateThemeColor"
        ></view>
        <view 
          class="color-item {{styleConfig.themeColor === '#D69E2E' ? 'active' : ''}}" 
          style="background-color: #D69E2E;"
          data-color="#D69E2E"
          bindtap="updateThemeColor"
        ></view>
      </view>
    </view>
    
    <!-- 重命名设置 -->
    <view class="panel-content" wx:if="{{currentOptionId === 'rename'}}">
      <view class="panel-title">重命名简历</view>
      <input 
        class="rename-input" 
        value="{{styleConfig.resumeName}}" 
        placeholder="输入简历名称"
        bindinput="renameResume"
      />
      <button class="confirm-btn" bindtap="toggleOption" data-id="">确定</button>
    </view>
    
    <!-- 字号间距设置 -->
    <view class="panel-content" wx:if="{{currentOptionId === 'spacing'}}">
      <view class="panel-title">字号与间距</view>
      <view class="slider-container">
        <view class="slider-label">字体大小</view>
        <slider 
          min="12" 
          max="18" 
          value="{{parseInt(styleConfig.fontSize)}}" 
          show-value 
          bindchange="updateFontSize"
        />
      </view>
      <view class="slider-container">
        <view class="slider-label">字符间距</view>
        <slider 
          min="0" 
          max="5" 
          step="0.5"
          value="{{styleConfig.letterSpacing === 'normal' ? 0 : parseFloat(styleConfig.letterSpacing)}}" 
          show-value 
          bindchange="updateLetterSpacing"
        />
      </view>
    </view>
    
    <!-- 封面设置 -->
    <view class="panel-content" wx:if="{{currentOptionId === 'cover'}}">
      <view class="panel-title">封面设置</view>
      <view class="switch-container">
        <view class="switch-label">启用封面</view>
        <switch checked="{{styleConfig.enableCover}}" bindchange="toggleCover" color="#2B6CB0"/>
      </view>
    </view>
  </view>
  
  <!-- 底部操作栏 -->
  <view class="bottom-toolbar">
    <view 
      class="toolbar-item {{currentOptionId === option.id ? 'active' : ''}}" 
      wx:for="{{bottomOptions}}" 
      wx:key="id" 
      wx:for-item="option"
      data-id="{{option.id}}"
      bindtap="{{option.id === 'download' ? 'downloadDocument' : 'toggleOption'}}"
    >
      <view class="toolbar-icon {{option.icon}}"></view>
      <view class="toolbar-text">{{option.name}}</view>
    </view>
  </view>
</view>