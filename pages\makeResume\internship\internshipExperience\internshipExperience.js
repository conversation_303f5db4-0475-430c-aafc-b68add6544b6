Page({
  data: {
    internshipFormData: [],
    currentIndex: -1,
    startY: 0,
    moveY: 0
  },

  onLoad() {
    const internshipFormData = wx.getStorageSync('internshipList') || [];
    this.setData({ internshipFormData });
  },

  // 长按触发移动
  handleLongPress(e) {
    const index = e.currentTarget.dataset.index;
    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY
    });
    wx.vibrateShort(); // 添加震动反馈
  },

  // 触摸移动中
  touchMove(e) {
    if (this.data.currentIndex < 0) return;

    const moveY = e.touches[0].clientY;
    const moveDistance = moveY - this.data.startY;

    // 计算目标位置
    const itemHeight = 50; // 每个项目的高度，根据实际调整
    const moveIndex = Math.round(moveDistance / itemHeight);

    let targetIndex = this.data.currentIndex + moveIndex;
    targetIndex = Math.max(0, Math.min(targetIndex, this.data.internshipFormData.length - 1));

    if (targetIndex !== this.data.currentIndex) {
      // 交换位置
      const internshipFormData = [...this.data.internshipFormData];
      const temp = internshipFormData[this.data.currentIndex];
      internshipFormData[this.data.currentIndex] = internshipFormData[targetIndex];
      internshipFormData[targetIndex] = temp;

      this.setData({
        internshipFormData,
        currentIndex: targetIndex,
        startY: moveY
      });
    }
  },

  // 触摸结束
  touchEnd() {
    if (this.data.currentIndex >= 0) {
      // 更新所有项的sortIndex
      const internshipFormData = this.data.internshipFormData.map((item, index) => ({
        ...item,
        sortIndex: index
      }));

      // 保存排序后的数据
      this.setData({
        internshipFormData,
        currentIndex: -1,
        startY: 0,
        moveY: 0
      });

      // 更新存储
      wx.setStorageSync('internshipList', internshipFormData);
    }
  },

  // 添加实习经历
  addInternship() {
    wx.navigateTo({
      url: '../internshipEdit/internshipEdit'
    });
  },

  // 编辑实习经历
  editInternship(e) {
    const index = e.currentTarget.dataset.index;
    wx.navigateTo({
      url: `../internshipEdit/internshipEdit?index=${index}`
    });
  },

  // 删除实习经历
  deleteInternship(e) {
    const index = e.currentTarget.dataset.index;
    wx.showModal({
      title: '提示',
      content: '确定要删除这条实习经历吗？',
      success: (res) => {
        if (res.confirm) {
          const internshipFormData = this.data.internshipFormData;
          internshipFormData.splice(index, 1);
          this.setData({ internshipFormData });
          wx.setStorageSync('internshipList', internshipFormData);

          // 同时同步到resumeManager
          const resumeManager = require('../../../../utils/resume/resumeManager.js');
          const currentResumeData = resumeManager.getCurrentResumeData() || {};
          currentResumeData.internships = internshipFormData;
          resumeManager.saveCurrentResumeData(currentResumeData);

          console.log('=== 实习经历删除同步 ===');
          console.log('删除后的实习经历数据:', internshipFormData);
        }
      }
    });
  },

  // 页面显示时刷新数据
  onShow() {
    const internshipFormData = wx.getStorageSync('internshipList') || [];
    this.setData({ internshipFormData });
  }
});