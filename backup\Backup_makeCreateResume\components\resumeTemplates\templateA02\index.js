const templateConfig = {
  id: 'A02',
  name: '模板A02',
  version: '1.0.0',
  thumbnail: '',
  styles: {
    global: {
      fontFamily: 'Microsoft YaHei',
      fontSize: '12rpx',
      lineHeight: '1',
      color: '#333333',
      backgroundColor: '#ffffff',
      padding: '30rpx'
    }
  },
  supportedModules: [
    'basicInfo',
    'jobIntention',
    'educationList',
    'workList',
    'projectList',
    'skillsList',
    'evaluation',
    'custom1',
    'custom2',
    'custom3'
  ]
};

Component({
  properties: {
    resumeData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (!newVal) return;
        console.log('========== templateA02接收到的简历数据 ==========');
        console.log(newVal);
        
        console.log('========== templateA02接收到的moduleOrders数据 ==========');
        if (newVal.moduleOrders) {
          console.log('moduleOrders:', newVal.moduleOrders);
          Object.entries(newVal.moduleOrders).forEach(([moduleType, order]) => {
            console.log(`模块类型: ${moduleType}, 排序值: ${order}`);
          });
          
          // 处理模块排序
          this.handleModuleSort(newVal);
        } else {
          console.log('警告：templateA02未接收到moduleOrders数据');
        }
        console.log('=========================================');
      }
    },
    config: {
      type: Object,
      value: templateConfig,
      observer: function(newVal, oldVal) {
        if (!newVal || !oldVal) return;
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return;
        this.updateStyle(newVal);
      }
    }
  },

  data: {
    customStyle: '',
    templateConfig: templateConfig,
    sortedModules: [] // 用于存储排序后的模块
  },

  methods: {
    updateStyle(config) {
      if (!config) return;
      
      let fontSize = config.fontSize;
      if (typeof fontSize === 'string' && !fontSize.includes('rpx')) {
        if (fontSize.includes('px')) {
          const size = parseInt(fontSize.replace('px', ''));
          fontSize = (size * 2) + 'rpx';
        } else {
          fontSize = parseInt(fontSize) * 2 + 'rpx';
        }
      }

      const style = `
        --theme-color: ${config.themeColor || '#4B8BF5'};
        --font-size: ${fontSize || '12rpx'};
        --spacing: ${config.spacing || 1};
      `;

      this.setData({
        customStyle: style
      });
    },

    // getTemplateStyles() {
    //   return `
    //     .resumeTemplateA02 {
    //       width: 100%;
    //       aspect-ratio: 1 / 1.4142;
    //       max-width: 794px;
    //       margin: 0 auto;
    //       padding: 20rpx;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--text-color, #333333);
    //       box-sizing: border-box;
    //       background: white;
    //     }

    //     @media print {
    //       .resumeTemplateA02 {
    //         width: 210mm;
    //         height: 297mm;
    //         padding: 10mm;
    //         margin: 0;
    //       }
    //     }

    //     .basic-info {
    //       display: flex;
    //       align-items: flex-start;
    //       margin-bottom: 30rpx;
    //     }

    //     .avatar {
    //       width: 120rpx;
    //       height: 120rpx;
    //       border-radius: 60rpx;
    //       margin-right: 20rpx;
    //     }

    //     .info-content {
    //       flex: 1;
    //     }

    //     .info {
    //       display: flex;
    //       flex-wrap: wrap;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--secondary-color, #666666);
    //     }

    //     .section {
    //       margin-bottom: 30rpx;
    //     }

    //     .title {
    //       font-size: calc(var(--font-size, 12rpx) + 4rpx);
    //       line-height: var(--spacing, 1.5);
    //       font-weight: bold;
    //       margin-bottom: 20rpx;
    //       color: var(--theme-color, #2B6CB0);
    //     }

    //     .content {
    //       margin-bottom: 15rpx;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--text-color, #333333);
    //     }

    //     .school, .company, .projectName, .custom-name, .activity {
    //       font-weight: bold;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .degree, .time {
    //       color: var(--secondary-color, #666666);
    //       font-size: calc(var(--font-size, 12rpx) - 2rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .courses {
    //       color: var(--text-color, #333333);
    //       margin-top: 5rpx;
    //       font-size: calc(var(--font-size, 12rpx) - 2rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .skill {
    //       display: inline-block;
    //       margin-right: 20rpx;
    //       margin-bottom: 10rpx;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--text-color, #333333);
    //     }

    //     .awardName {
    //       font-weight: bold;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .awardInfo {
    //       color: var(--secondary-color, #666666);
    //       font-size: calc(var(--font-size, 12rpx) - 2rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .interestName {
    //       font-weight: bold;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .description {
    //       color: var(--text-color, #333333);
    //       margin-top: 5rpx;
    //       white-space: pre-wrap;
    //       word-break: break-all;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }
    //   `;
    // },

    // 添加模块排序处理方法
    handleModuleSort(resumeData) {
      if (!resumeData || !resumeData.moduleOrders) return;

      // 检查模块是否有内容的辅助函数
      const hasContent = (data, type) => {
        if (!data) return false;
        
        switch(type) {
          case 'basicInfo':
            return data.name || data.phone || data.email || data.photoUrl || 
                   data.birthday || data.marriage || data.politics || data.city || 
                   data.nation || data.age || data.hometown || data.gender || 
                   data.height || data.wechat || data.weight;
          case 'jobIntention':
            return data.position || data.salary || data.city || data.status;
          case 'education':
          case 'school':
          case 'internship':
          case 'work':
          case 'project':
            return Array.isArray(data) && data.length > 0;
          case 'skills':
          case 'awards':
          case 'interests':
            return Array.isArray(data) && data.length > 0;
          case 'evaluation':
            return Array.isArray(data) && data.length > 0 && data[0].content;
          case 'custom1':
          case 'custom2':
          case 'custom3':
            if (!resumeData.custom) return false;
            const customData = resumeData.custom[type];
            return Array.isArray(customData) && customData.length > 0 && (customData[0].content || customData[0].role || customData[0].customName);
          default:
            return false;
        }
      };

      // 获取所有模块及其排序值
      const moduleTypes = Object.keys(resumeData.moduleOrders);
      const sortedModules = moduleTypes
        .map(type => {
          let moduleData = resumeData[type];
          // 特殊处理自定义模块
          if (type.startsWith('custom') && resumeData.custom) {
            moduleData = resumeData.custom[type];
          }
          return {
            type,
            order: resumeData.moduleOrders[type],
            data: moduleData
          };
        })
        .filter(module => hasContent(module.data, module.type))
        .sort((a, b) => a.order - b.order);

      this.setData({ sortedModules });
    }
  }
}); 