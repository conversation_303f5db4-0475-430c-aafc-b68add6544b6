// 服务器 URL 配置
const apiConfig = {
    dev: {
        // 基础URL
        baseUrl: 'http://gbw8848.cn',

        // 简历相关API
        renderTemplateUrl: 'http://gbw8848.cn/resume/renderTemplateA01', // 开发环境
        generatePDFUrl: 'http://gbw8848.cn/resume/export-pdf',
        exportJpegUrl: 'http://gbw8848.cn/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: 'http://gbw8848.cn/user/login', // 用户登录
        userInfoUrl: 'http://gbw8848.cn/user/info', // 获取用户信息
        updateUserInfoUrl: 'http://gbw8848.cn/user/update', // 更新用户信息



        // 反馈API
        feedbackUrl: 'http://gbw8848.cn/feedback/submit' // 提交用户反馈
    },
    test: {
        // 基础URL
        baseUrl: 'http://192.168.1.218:18080',

        // 简历相关API
        renderTemplateUrl: 'http://192.168.1.218:18080/resume/renderTemplateA01', // 测试环境
        generatePDFUrl: 'http://192.168.1.218:18080/resume/export-pdf',
        exportJpegUrl: 'http://192.168.1.218:18080/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: '/auth/login', // 用户登录
        userInfoUrl: 'http://192.168.1.218:18080/auth/user', // 获取用户信息
        updateUserInfoUrl: 'http://192.168.1.218:18080/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl: 'http://192.168.1.218:18080/feedback/submit' // 提交用户反馈
    },

    test_out: {
        // 基础URL
        baseUrl: 'http://127.0.0.1:18080',

        // 简历相关API
        renderTemplateUrl:        '/resume/renderTemplateA01', // 测试环境
        generatePDFUrl:           '/resume/export-pdf',
        exportJpegUrl:            '/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl:                 '/auth/login', // 用户登录
        userInfoUrl:              '/auth/user', // 获取用户信息
        updateUserInfoUrl:        '/auth/user', // 更新用户信息

        // 反馈API
        feedbackUrl:              '/feedback/submit' // 提交用户反馈
    },


    prod: {
        // 简历相关API
        renderTemplateUrl: 'https://gbw8848.cn/resume/renderTemplateA01', // 生产环境
        generatePDFUrl: 'https://gbw8848.cn/resume/export-pdf',
        exportJpegUrl: 'https://gbw8848.cn/resume/export-jpeg', // 新增JPEG预览图片API

        // 用户相关API
        loginUrl: 'https://gbw8848.cn/user/login', // 用户登录
        userInfoUrl: 'https://gbw8848.cn/user/info', // 获取用户信息
        updateUserInfoUrl: 'https://gbw8848.cn/user/update', // 更新用户信息



        // 反馈API
        feedbackUrl: 'https://gbw8848.cn/feedback/submit' // 提交用户反馈
    }
};

// 根据环境导出配置
const env = 'test_out'; // 默认使用开发环境
module.exports = apiConfig[env];