<view class="container">
  <view class="formGroup">
    <!-- 经历时间 -->
    <view class="formItem">
      <text class="label">经历时间</text>
      <view class="datePicker">
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{schoolEditFormData.startDate}}" data-field="startDate" bindchange="handleDateChange">
            <view class="picker {{schoolEditFormData.startDate ? '' : 'placeholder'}}">
              {{schoolEditFormData.startDate || '请选择'}}
            </view>
          </picker>
        </view>
        <text class="separator">至</text>
        <view class="dateSection">
          <picker mode="date" fields="month" value="{{schoolEditFormData.endDate}}" data-field="endDate" bindchange="handleDateChange">
            <view class="picker {{schoolEditFormData.endDate ? '' : 'placeholder'}}">
              {{schoolEditFormData.endDate || '请选择'}}
            </view>
          </picker>
        </view>
        <view class="nowBtn" bindtap="setEndDateToNow">至今</view>
      </view>
    </view>

    <!-- 担任角色 -->
    <view class="formItem">
      <text class="label">担任角色</text>
      <input class="input" placeholder="如社团、职务、奖学金、获奖、竞赛等" value="{{schoolEditFormData.role}}" data-field="role" bindinput="handleInput"/>
    </view>

    <!-- 经历描述 -->
    <view class="formItem">
      <text class="label">经历描述</text>
      <view class="editorToolbar">
        <view class="toolbarBtn" data-name="italic" bindtap="handleFormat">I</view>
        <view class="toolbarBtn" data-name="bold" bindtap="handleFormat">B</view>
        <view class="toolbarBtn" data-name="underline" bindtap="handleFormat">U</view>
        <view class="toolbarBtn" data-name="list" bindtap="handleFormat">⅓</view>
        <view class="toolbarBtn" data-name="indent" bindtap="handleFormat">≡</view>
      </view>
      <editor id="editor" 
              class="editor" 
              placeholder="请输入详细内容描述。"
              show-img-size
              show-img-toolbar
              show-img-resize
              bindready="onEditorReady"
              bindinput="handleEditorInput">
      </editor>
    </view>
  </view>

  <!-- 底部按钮 -->
  <view class="buttonGroup">
    <button class="saveBtn" bindtap="saveSchool">保存信息</button>
    <button wx:if="{{isEdit}}" class="deleteBtn" bindtap="deleteInfo">删除</button>
  </view>
</view> 