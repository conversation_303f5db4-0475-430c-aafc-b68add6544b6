Component({
  properties: {
    resumeData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (!newVal) return;
        if (newVal.moduleOrders) {
          this.handleModuleSort(newVal);
        }
      }
    },
    config: {
      type: Object,
      value: {},
      observer: function(newVal, oldVal) {
        if (!newVal || !oldVal) return;
        if (JSON.stringify(newVal) !== JSON.stringify(oldVal)) {
          this.updateStyle(newVal);
        }
      }
    },
    templateConfig: {
      type: Object,
      value: {}
    }
  },

  data: {
    customStyle: '',
    sortedModules: []
  },

  methods: {
    updateStyle(config) {
      if (!config) return;
      
    //   let fontSize = config.fontSize;
    //   if (typeof fontSize === 'string' && !fontSize.includes('rpx')) {
    //     if (fontSize.includes('px')) {
    //       const size = parseInt(fontSize.replace('px', ''));
    //       fontSize = (size * 2) + 'rpx';
    //     } else {
    //       fontSize = parseInt(fontSize) * 2 + 'rpx';
    //     }
    //   }

      const style = `
        --theme-color: ${config.themeColor || '#4B8BF5'};
        --font-size: ${config.fontSize || 11};
        --spacing: ${config.spacing || 1};
      `;

      this.setData({
        customStyle: style
      });
    },

    handleModuleSort(resumeData) {
      if (!resumeData || !resumeData.moduleOrders) return;

      // 检查模块是否有内容的辅助函数
      const hasContent = (data, type) => {
        if (!data) return false;
        
        switch(type) {
          case 'basicInfo':
            return data.name || data.phone || data.email || data.photoUrl || 
                   data.birthday || data.marriage || data.politics || data.city || 
                   data.nation || data.age || data.hometown || data.gender || 
                   data.height || data.wechat || data.weight;
          case 'jobIntention':
            return data.position || data.salary || data.city || data.status;
          case 'education':
          case 'school':
          case 'internship':
          case 'work':
          case 'project':
            return Array.isArray(data) && data.length > 0;
          case 'skills':
          case 'awards':
          case 'interests':
            return Array.isArray(data) && data.length > 0;
          case 'evaluation':
            return Array.isArray(data) && data.length > 0 && data[0].content;
          case 'custom1':
          case 'custom2':
          case 'custom3':
            return data.content || data.role || data.title;
          default:
            return false;
        }
      };

      // 创建模块映射关系
      const moduleMapping = {
        basicInfo: resumeData.basicInfo,
        jobIntention: resumeData.jobIntention,
        school: resumeData.school,
        education: resumeData.education,
        internship: resumeData.internship,
        work: resumeData.work,
        awards: resumeData.awards,
        project: resumeData.project,
        skills: resumeData.skills,
        evaluation: resumeData.evaluation,
        interests: resumeData.interests,
        // 处理自定义模块数据
        custom1: resumeData.custom?.custom1?.[0] ? {
          title: resumeData.custom.custom1[0].customName || '自定义模块1',
          role: resumeData.custom.custom1[0].role,
          startDate: resumeData.custom.custom1[0].startDate,
          endDate: resumeData.custom.custom1[0].endDate,
          content: resumeData.custom.custom1[0].content
        } : null,
        custom2: resumeData.custom?.custom2?.[0] ? {
          title: resumeData.custom.custom2[0].customName || '自定义模块2',
          role: resumeData.custom.custom2[0].role,
          startDate: resumeData.custom.custom2[0].startDate,
          endDate: resumeData.custom.custom2[0].endDate,
          content: resumeData.custom.custom2[0].content
        } : null,
        custom3: resumeData.custom?.custom3?.[0] ? {
          title: resumeData.custom.custom3[0].customName || '自定义模块3',
          role: resumeData.custom.custom3[0].role,
          startDate: resumeData.custom.custom3[0].startDate,
          endDate: resumeData.custom.custom3[0].endDate,
          content: resumeData.custom.custom3[0].content
        } : null
      };

      // 构建排序模块数组
      const modules = Object.entries(resumeData.moduleOrders)
        .map(([type, order]) => ({
          type,
          order,
          data: moduleMapping[type]
        }))
        .filter(module => hasContent(module.data, module.type))
        .sort((a, b) => a.order - b.order);

      this.setData({
        sortedModules: modules
      });
    }
  },
  
  // 生命周期方法
  lifetimes: {
    attached() {
      if (this.data.config) {
        this.updateStyle(this.data.config);
      }
    }
  }
});
