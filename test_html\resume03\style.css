/* 全局样式 */
:root {
    --theme-color: #44546b;
    --base-font-size: 13pt;
    --font-size: 13pt;
    --spacing: 1.5;
    --text-color: #000000;
    --secondary-color: #e44c4c;
}

@page {
    size: A4;
    margin: 0;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    /* font-family: "Microsoft YaHei", "SimHei", sans-serif; */
}

body {
    background-color: #ffffff;
    display: flex;
    /* flex-direction: column; */

    justify-content: center;
    padding: 0px;
}



.resume-container {
    width: 210mm;
    min-height: 297mm;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
    flex-direction: column; 
}

.resume-title {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    background-color: #44546b;
    color: white;
    padding: 10px 20px;
    width: 100%;
}

.resume-title h1 {
    margin: 0;
    font-size: 24px;
    font-weight: bold;
    align-self: self-end;
}

/* SVG图标的通用样式 */
.header-icons img{
    fill: white;  /* 设置填充颜色 */
    stroke: white; /* 设置描边颜色（如果需要） */
    margin-right: 10px; /* 图标间距 */
    filter: brightness(0) invert(1);
}

.icon-1 {
    width: 26px;  /* 设置宽度 */
    height: 26px; /* 设置高度 */
}
.icon-2 {
    width: 20px;  /* 设置宽度 */
    height: 20px; /* 设置高度 */
}
.icon-3 {
    width: 14px;  /* 设置宽度 */
    height: 14px; /* 设置高度 */
}
.icon-4 {
    width: 8px;  /* 设置宽度 */
    height: 8px; /* 设置高度 */
}
/* 如果使用img标签引入SVG
.header-icons img.icon {
    filter: brightness(0) invert(1); /* 将图标变为白色 */

.left-column .icon-1 {
    width: 26px;  /* 设置宽度 */
    height: 26px; /* 设置高度 */
    margin-right: 10px;
}


.resume-title h1 {
    margin: 0;
    font-size: 24px;
}

.content {
    display: flex;
    flex: 1;
}
/* 调整左右栏容器 */
/* .columns-container {
    display: flex;
    flex: 1;
} */

/* 左侧栏样式 */
.left-column {
    /* width: 30%; */
    flex: 3;
    background-color: #f0f0f0;
    padding-bottom: 20px;
}

.photo-container {
    padding: 20px;
    margin: 20px;
    display: flex;
    justify-content: center;
}

.photo-container img {
    width: 150px;
    height: 200px;
    object-fit: cover;
    border: 1px solid #f0f0f0;
}

.name-container {
    text-align: center;
    padding: 10px 0;
    /* background-color: #fff; */
    color: black;
    margin: 20px;
}

.name-container h1 {
    font-size: 28px;
    font-weight: bold;
}

/* 右侧栏样式 */
.right-column {
    /* width: 70%; */
    flex: 7;
    padding: 20px;
}

/* 通用部分样式 */
.section {
    margin-bottom: 20px;
}

.section-tile-and-line {
    display: flex;
    background-color: #ffffff;
    /* align-items: center; */
    /* justify-content: space-between; */
    /* align-self: center; */
    margin-bottom: 10px;
    /* margin-top: 5px; */
}

.section-title {
    display: flex;
    align-items: center;
    padding: 8px 15px;
    background-color: #44546b;
    color: white;
    border-radius: 4px;
    width: 200px;
}

.section-title i {
    margin-right: 10px;
    font-size: 18px;
}

.section-title h2 {
    font-size: 18px;
    font-weight: normal;
}

.section-title-line {
    width: 100%;
    height: 1px;
    background-color: #44546b;
    margin-right: 20px;
    align-self: center;
}

.section-content {
    padding: 0 15px;
    color: black;
}

.left-column .section-content {
    font-size: 14px;
    line-height: 3.0;
}

.left-column .section-content p {
    display: flex;
    flex-wrap: wrap;

    /* text-wrap: wrap; */
}

.left-column .section-content p span:first-child {
    width: 75px;
    font-weight: bold;
}

#contact-section-content p span:first-child {
    width: auto;
    font-weight: bold;
}

.left-column-strip {
    width: 100%;
    height: 18px;
    background-color: #44546b;
    /* margin-left: 20px; */
    /* margin-right: 20px; */
    border-radius: 4px;
}

.left-column-title {
    width: 100%;
    height: 50px;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 8px 15px;
    /* border-radius: 4px; */
    margin-bottom: 10px;
}

.left-column-title i {
    margin-right: 10px;
    font-size: 18px;
}

.left-column-title h2 {
    font-size: 18px;
    font-weight: bold;
    color: black;
}






/* 教育背景样式 */
.education-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-weight: bold;
}

.education-courses {
    line-height: 1.6;
}

/* 在校经历样式 */
.experience-item {
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}

.experience-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #44546b;
    border-radius: 50%;
}

/* 技能证书样式 */
/* #skills-section .section-content p {
    margin-bottom: 10px;
    line-height: 1.6;
} */

.skill-item {
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}

.skill-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #44546b;
    border-radius: 50%;
}


/* 自我评价样式 */
.evaluation-item {
    margin-bottom: 15px;
    position: relative;
    padding-left: 20px;
}

.evaluation-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #44546b;
    border-radius: 50%;
}

/* 隐藏空模块 */
.hidden {
    display: none;
} 