const templateConfig = {
  id: 'A01',
  name: '模板A01',
  version: '1.0.0',
  thumbnail: '',
  styles: {
    global: {
      fontFamily: 'Microsoft YaHei',
      fontSize: '12rpx',
      lineHeight: '1',
      color: '#333333',
      backgroundColor: '#ffffff',
      padding: '30rpx'
    }
  },
  supportedModules: [
    'basicInfo',
    'jobIntention',
    'educationList',
    'workList',
    'projectList',
    'skillsList',
    'evaluation'
  ]
};

Component({
  properties: {
    resumeData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (!newVal) return;
        console.log('========== templateA01接收到的简历数据 ==========');
        console.log(newVal);
        
        console.log('========== templateA01接收到的moduleOrders数据 ==========');
        if (newVal.moduleOrders) {
          console.log('moduleOrders:', newVal.moduleOrders);
          Object.entries(newVal.moduleOrders).forEach(([moduleType, order]) => {
            console.log(`模块类型: ${moduleType}, 排序值: ${order}`);
          });
          
          // 处理模块排序
          this.handleModuleSort(newVal);
        } else {
          console.log('警告：templateA01未接收到moduleOrders数据');
        }
        console.log('=========================================');
      }
    },
    config: {
      type: Object,
      value: templateConfig,
      observer: function(newVal, oldVal) {
        if (!newVal || !oldVal) return;
        if (JSON.stringify(newVal) === JSON.stringify(oldVal)) return;
        this.updateStyle(newVal);
      }
    }
  },

  data: {
    customStyle: '',
    templateConfig: templateConfig,
    sortedModules: [] // 用于存储排序后的模块
  },

  methods: {
    updateStyle(config) {
      if (!config) return;
      
      let fontSize = config.fontSize;
      if (typeof fontSize === 'string' && !fontSize.includes('rpx')) {
        if (fontSize.includes('px')) {
          const size = parseInt(fontSize.replace('px', ''));
          fontSize = (size * 2) + 'rpx';
        } else {
          fontSize = parseInt(fontSize) * 2 + 'rpx';
        }
      }

      const style = `
        --theme-color: ${config.themeColor || '#4B8BF5'};
        --font-size: ${fontSize || '12rpx'};
        --spacing: ${config.spacing || 1};
      `;

      this.setData({
        customStyle: style
      });
    },

    // getTemplateStyles() {
    //   return `
    //     .resumeTemplateA01 {
    //       width: 100%;
    //       aspect-ratio: 1 / 1.4142;
    //       max-width: 794px;
    //       margin: 0 auto;
    //       padding: 20rpx;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--text-color, #333333);
    //       box-sizing: border-box;
    //       background: white;
    //     }

    //     @media print {
    //       .resumeTemplateA01 {
    //         width: 210mm;
    //         height: 297mm;
    //         padding: 10mm;
    //         margin: 0;
    //       }
    //     }

    //     .basic-info {
    //       display: flex;
    //       align-items: flex-start;
    //       margin-bottom: 30rpx;
    //     }

    //     .avatar {
    //       width: 120rpx;
    //       height: 120rpx;
    //       border-radius: 60rpx;
    //       margin-right: 20rpx;
    //     }

    //     .info-content {
    //       flex: 1;
    //     }

    //     .info {
    //       display: flex;
    //       flex-wrap: wrap;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--secondary-color, #666666);
    //     }

    //     .section {
    //       margin-bottom: 30rpx;
    //     }

    //     .title {
    //       font-size: calc(var(--font-size, 12rpx) + 4rpx);
    //       line-height: var(--spacing, 1.5);
    //       font-weight: bold;
    //       margin-bottom: 20rpx;
    //       color: var(--theme-color, #2B6CB0);
    //     }

    //     .content {
    //       margin-bottom: 15rpx;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--text-color, #333333);
    //     }

    //     .school, .company, .projectName, .custom-name, .activity {
    //       font-weight: bold;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .degree, .time {
    //       color: var(--secondary-color, #666666);
    //       font-size: calc(var(--font-size, 12rpx) - 2rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .courses {
    //       color: var(--text-color, #333333);
    //       margin-top: 5rpx;
    //       font-size: calc(var(--font-size, 12rpx) - 2rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .skill {
    //       display: inline-block;
    //       margin-right: 20rpx;
    //       margin-bottom: 10rpx;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //       color: var(--text-color, #333333);
    //     }

    //     .awardName {
    //       font-weight: bold;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .awardInfo {
    //       color: var(--secondary-color, #666666);
    //       font-size: calc(var(--font-size, 12rpx) - 2rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .interestName {
    //       font-weight: bold;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }

    //     .description {
    //       color: var(--text-color, #333333);
    //       margin-top: 5rpx;
    //       white-space: pre-wrap;
    //       word-break: break-all;
    //       font-size: var(--font-size, 12rpx);
    //       line-height: var(--spacing, 1.5);
    //     }
    //   `;
    // },

    // 添加模块排序处理方法
    handleModuleSort(resumeData) {
      if (!resumeData || !resumeData.moduleOrders) return;

      // 检查模块是否有内容的辅助函数
      const hasContent = (data, type) => {
        if (!data) return false;
        
        switch(type) {
          case 'basicInfo':
            return data.name || data.phone || data.email || data.photoUrl || 
                   data.birthday || data.marriage || data.politics || data.city || 
                   data.nation || data.age || data.hometown || data.gender || 
                   data.height || data.wechat || data.weight;
          case 'jobIntention':
            return data.position || data.salary || data.city || data.status;
          case 'education':
          case 'school':
          case 'internship':
          case 'work':
          case 'project':
            return Array.isArray(data) && data.length > 0;
          case 'skills':
          case 'awards':
          case 'interests':
            return Array.isArray(data) && data.length > 0;
          case 'evaluation':
            return Array.isArray(data) && data.length > 0 && data[0].content;
          case 'custom1':
          case 'custom2':
          case 'custom3':
            return data.content || data.role || data.title;
          default:
            return false;
        }
      };

      // 创建模块映射关系
      const moduleMapping = {
        basicInfo: resumeData.basicInfo,
        jobIntention: resumeData.jobIntention,
        school: resumeData.school,
        education: resumeData.education,
        internship: resumeData.internship,
        work: resumeData.work,
        awards: resumeData.awards,
        project: resumeData.project,
        skills: resumeData.skills,
        evaluation: resumeData.evaluation,
        interests: resumeData.interests,
        // 处理自定义模块数据
        custom1: resumeData.custom?.custom1?.[0] ? {
          title: resumeData.custom.custom1[0].customName || '自定义模块1',
          role: resumeData.custom.custom1[0].role,
          startDate: resumeData.custom.custom1[0].startDate,
          endDate: resumeData.custom.custom1[0].endDate,
          content: resumeData.custom.custom1[0].content
        } : null,
        custom2: resumeData.custom?.custom2?.[0] ? {
          title: resumeData.custom.custom2[0].customName || '自定义模块2',
          role: resumeData.custom.custom2[0].role,
          startDate: resumeData.custom.custom2[0].startDate,
          endDate: resumeData.custom.custom2[0].endDate,
          content: resumeData.custom.custom2[0].content
        } : null,
        custom3: resumeData.custom?.custom3?.[0] ? {
          title: resumeData.custom.custom3[0].customName || '自定义模块3',
          role: resumeData.custom.custom3[0].role,
          startDate: resumeData.custom.custom3[0].startDate,
          endDate: resumeData.custom.custom3[0].endDate,
          content: resumeData.custom.custom3[0].content
        } : null
      };

      // 构建排序模块数组
      const modules = Object.entries(resumeData.moduleOrders)
        .map(([type, order]) => ({
          type,
          order,
          data: moduleMapping[type]
        }))
        .filter(module => hasContent(module.data, module.type)) // 使用hasContent函数过滤空模块
        .sort((a, b) => a.order - b.order); // 根据order排序

      this.setData({
        sortedModules: modules
      });
    }
  }
}); 