<!-- pages/user/settings/settings.wxml -->
<view class="container">
  <!-- 用户信息区域 -->
  <view class="user-info-section" wx:if="{{hasUserInfo}}">
    <image class="avatar" src="{{userInfo.avatarUrl}}" mode="aspectFill"></image>
    <text class="nickname">{{userInfo.nickName}}</text>
  </view>
  
  <!-- 设置项区域 -->
  <view class="settings-section">
    <view class="section-title">应用设置</view>
    
    <!-- 自动保存设置 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">自动保存</text>
        <text class="setting-desc">编辑简历时自动保存内容</text>
      </view>
      <switch checked="{{settings.autoSave}}" bindchange="toggleSetting" data-key="autoSave" color="#4B8BF5"></switch>
    </view>
    
    <!-- 数据同步设置 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">数据同步</text>
        <text class="setting-desc">将简历数据同步到云端</text>
      </view>
      <switch checked="{{settings.dataSync}}" bindchange="toggleSetting" data-key="dataSync" color="#4B8BF5"></switch>
    </view>
    
    <!-- 使用记录设置 -->
    <view class="setting-item">
      <view class="setting-info">
        <text class="setting-label">使用记录</text>
        <text class="setting-desc">记录使用情况以改进服务</text>
      </view>
      <switch checked="{{settings.usageTracking}}" bindchange="toggleSetting" data-key="usageTracking" color="#4B8BF5"></switch>
    </view>
  </view>
  
  <!-- 账号操作区域 -->
  <view class="account-section">
    <view class="section-title">账号操作</view>
    
    <!-- 清除缓存 -->
    <view class="action-item" bindtap="clearCache">
      <text class="action-label">清除缓存</text>
      <view class="action-arrow"></view>
    </view>
    
    <!-- 退出登录 -->
    <view class="action-item" bindtap="logout">
      <text class="action-label logout-label">退出登录</text>
      <view class="action-arrow"></view>
    </view>
  </view>
  
  <!-- 关于区域 -->
  <view class="about-section">
    <view class="section-title">关于</view>
    
    <!-- 关于我们 -->
    <view class="action-item" bindtap="showAbout">
      <text class="action-label">关于我们</text>
      <view class="action-arrow"></view>
    </view>
    
    <!-- 用户协议 -->
    <view class="action-item" bindtap="showUserAgreement">
      <text class="action-label">用户协议</text>
      <view class="action-arrow"></view>
    </view>
    
    <!-- 隐私政策 -->
    <view class="action-item" bindtap="showPrivacyPolicy">
      <text class="action-label">隐私政策</text>
      <view class="action-arrow"></view>
    </view>
  </view>
  
  <!-- 版本信息 -->
  <view class="version-info">
    <text>版本 1.0.0</text>
  </view>
</view>
