document.addEventListener('DOMContentLoaded', function() {
    // 示例数据，实际使用时可以从外部加载
    const resumeData = {
        // 基本信息
        name: "李甜甜",
        avatar: "placeholder-avatar.jpg", // 实际使用时替换为真实图片路径
        phone: "13X-XXXX-XXXX",
        email: "XXXXxxxxxxxxxXXXX@XXX",
        
        // 个人信息
        personalInfo: {
            ethnicity: "汉",
            hometown: "上海",
            currentLocation: "上海虹口区",
            jobIntention: "某某岗位",
            birthday: "200x年 xx月 xx日",
            height: "168cm",
            educationLevel: "本科",
            politicalStatus: "党员"
        },
        
        // 教育背景
        education: {
            time: "20XX.09-20XX.06",
            school: "XX大学",
            major: "XX专业（本科）",
            courses: "填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程、填写课程等等。"
        },
        
        // 在校经历
        experiences: [
            {
                time: "20XX.09",
                content: "担任班级学习委员,负责协调班级课业及文体活动,团结同学共渡大学生活;"
            },
            {
                time: "20XX.03",
                content: "在校报编辑部担任记者,完成过多篇采访报道,例如校园新生专题报道等;"
            },
            {
                time: "20XX.05",
                content: "担任音乐社副社长,协助社长组织音乐交流活动,并代表学院参加校园歌手大赛,进入总决赛;"
            },
            {
                time: "20XX.03",
                content: "担任校园志愿者服务队成员,积极参与校内外公益活动,履行社会责任。"
            }
        ],
        
        // 技能证书
        skills: {
            certificates: "大学英语六级、大学生英语四级、全国计算机等级二级",
            personalSkills: "具备出色的语言表达能力; 拥有敏锐的观察力和丰富的想象力与创造力;",
            details: "注重细节, 能够专注处理各项任务, 保持冷静与稳定。"
        },
        
        // 自我评价
        evaluations: [
            {
                title: "学术能力:",
                content: "通过大学的学习，我掌握了专业所需的理论和实践技能，并能够将其应用于实际工作中。"
            },
            {
                title: "团队合作:",
                content: "大学期间，我积极参与团队项目和合作，共同完成任务并取得良好的结果。"
            },
            {
                title: "沟通能力:",
                content: "我善于倾听他人的意见和建议，并能够与他人进行有效的交流和合作，以达到共同的目标。"
            },
            {
                title: "学习能力:",
                content: "我具备自我驱动力，能够积极面对挑战并寻求自我成长的机会。"
            }
        ]
    };
    
    // 渲染基本信息
    document.getElementById('name').textContent = resumeData.name || '';
    document.getElementById('avatar').src = resumeData.avatar || 'placeholder-avatar.jpg';
    document.getElementById('phone').textContent = resumeData.phone || '';
    document.getElementById('email').textContent = resumeData.email || '';
    
    // 渲染个人信息
    const personalInfo = resumeData.personalInfo;
    if (personalInfo) {
        document.getElementById('ethnicity').textContent = personalInfo.ethnicity || '';
        document.getElementById('hometown').textContent = personalInfo.hometown || '';
        document.getElementById('current-location').textContent = personalInfo.currentLocation || '';
        document.getElementById('job-intention').textContent = personalInfo.jobIntention || '';
        document.getElementById('birthday').textContent = personalInfo.birthday || '';
        document.getElementById('height').textContent = personalInfo.height || '';
        document.getElementById('education-level').textContent = personalInfo.educationLevel || '';
        document.getElementById('political-status').textContent = personalInfo.politicalStatus || '';
    } else {
        document.getElementById('personal-info-section').classList.add('hidden');
    }
    
    // 渲染教育背景
    const education = resumeData.education;
    if (education) {
        document.getElementById('education-time').textContent = education.time || '';
        document.getElementById('education-school').textContent = education.school || '';
        document.getElementById('education-major').textContent = education.major || '';
        document.getElementById('main-courses').textContent = education.courses || '';
    } else {
        document.getElementById('education-section').classList.add('hidden');
    }
    
    // 渲染在校经历
    const experiences = resumeData.experiences;
    const experienceContent = document.getElementById('experience-content');
    if (experiences && experiences.length > 0) {
        experiences.forEach((exp, index) => {
            const expItem = document.createElement('div');
            expItem.className = 'experience-item';
            expItem.innerHTML = `
                <p>${index + 1}. ${exp.time} ${exp.content}</p>
            `;
            experienceContent.appendChild(expItem);
        });
    } else {
        document.getElementById('experience-section').classList.add('hidden');
    }
    
    // 渲染技能证书
    const skills = resumeData.skills;
    const skillsContent = document.getElementById('skills-section').querySelector('.section-content');
    if (skills && skills.certificates && skills.personalSkills) {
        // 清空原有内容
        skillsContent.innerHTML = '';
        
        // 添加证书信息
        const certItem = document.createElement('div');
        certItem.className = 'skill-item';
        certItem.innerHTML = `
            <p> 获得证书：${skills.certificates || ''}</p>
        `;
        skillsContent.appendChild(certItem);
        
        // 添加个人技能
        const skillItem = document.createElement('div');
        skillItem.className = 'skill-item';
        skillItem.innerHTML = `
            <p> 个人技能：${skills.personalSkills || ''}</p>
        `;
        skillsContent.appendChild(skillItem);
        
        // 添加技能详情（如果有）
        if (skills.details) {
            const detailItem = document.createElement('div');
            detailItem.className = 'skill-item';
            detailItem.innerHTML = `
                <p> 技能详情：${skills.details || ''}</p>
            `;
            skillsContent.appendChild(detailItem);
        }
    } else {
        document.getElementById('skills-section').classList.add('hidden');
    }
    
    // 渲染自我评价
    const evaluations = resumeData.evaluations;
    const evaluationContent = document.getElementById('self-evaluation-content');
    if (evaluations && evaluations.length > 0) {
        evaluations.forEach((eval, index) => {
            const evalItem = document.createElement('div');
            evalItem.className = 'evaluation-item';
            evalItem.innerHTML = `
                <p>${index + 1}. ${eval.title} ${eval.content}</p>
            `;
            evaluationContent.appendChild(evalItem);
        });
    } else {
        document.getElementById('self-evaluation-section').classList.add('hidden');
    }
    
    // 检查并隐藏空的联系方式部分
    if (!resumeData.phone && !resumeData.email) {
        document.getElementById('contact-section').classList.add('hidden');
    }
}); 