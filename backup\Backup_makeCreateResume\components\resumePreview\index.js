Component({
  properties: {
    resumeData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (!newVal) return;
        
        console.log('========== resumePreview接收到的数据 ==========');
        console.log('moduleOrders数据：', newVal.moduleOrders);
        
        console.log('========== moduleOrders详细数据 ==========');
        if (newVal.moduleOrders) {
          Object.entries(newVal.moduleOrders).forEach(([moduleType, order]) => {
            console.log(`模块类型: ${moduleType}, 排序值: ${order}`);
          });
        } else {
          console.log('未接收到moduleOrders数据');
        }
        console.log('=========================================');
        
        this.setData({ 
          currentResumeData: newVal 
        });
      }
    },
    template: {
      type: Object,
      value: {
        id: 'templateA01',
        name: '模板一'
      },
      observer: function(newVal) {
        if (newVal && newVal.id) {
          this.setData({ 
            currentTemplate: newVal.id
          });
        }
      }
    },
    config: {
      type: Object,
      value: {
        themeColor: '#2B6CB0',
        fontSize: '11',
        spacing: '1.2'
      },
      observer: function(newVal, oldVal) {
        if (!newVal || !oldVal) return;
        
        const needUpdate = 
          newVal.themeColor !== oldVal.themeColor ||
          newVal.fontSize !== oldVal.fontSize ||
          newVal.spacing !== oldVal.spacing;
          
        if (needUpdate) {
          this.updateStyle(newVal);
        }
      }
    }
  },

  data: {
    currentTemplate: 'templateA01',
    currentResumeData: {},
    customStyle: '',
    lastConfig: null
  },

  methods: {
    updateStyle(config) {
      if (!config) return;
      
      let fontSize = '14pt';
      if (config.fontSize) {
        const size = parseInt(config.fontSize.toString().replace('px', ''));
        fontSize = size + 'pt';
      }
      
      const style = `
        --theme-color: ${config.themeColor || '#4B6CB0'};
        --font-size: ${fontSize};
        --spacing: ${config.spacing || 1};
      `;
      
      this.setData({ 
        customStyle: style,
        lastConfig: { ...config }
      });

      const templateId = this.data.currentTemplate;
      const template = this.selectComponent(`#${templateId}`);
      if (template) {
        template.updateStyle({
          ...config,
          fontSize: fontSize
        });
      }
    },

    getResumeRenderData() {
      return {
        template: {
          id: this.data.currentTemplate,
          styles: {}
        },
        resumeData: this.data.currentResumeData,
        config: this.data.lastConfig || this.properties.config,
        customStyle: this.data.customStyle
      };
    }
  }
}); 