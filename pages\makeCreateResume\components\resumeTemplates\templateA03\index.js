const templateConfig = {
  id: 'A03',
  name: '模板A03',
  version: '1.0.0',
  thumbnail: '',
  styles: {
    global: {
      fontFamily: 'Microsoft YaHei',
      fontSize: 12,
      lineHeight: 1,
      color: '#333333',
      backgroundColor: '#ffffff',
      padding: '30rpx'
    }
  },
  supportedModules: [
    'basicInfo',
    'jobIntention',
    'educationList',
    'workList',
    'projectList',
    'skillsList',
    'evaluation'
  ]
};

// 引入基础模板行为
const resumeBaseTemplate = require('../../../behaviors/resumeBaseTemplate');

// 继承基础模板组件
Component({
  behaviors: [resumeBaseTemplate], // 使用behaviors继承基础模板的功能
  
  data: {
    templateConfig: templateConfig
  },
  
  lifetimes: {
    attached() {
      // 设置模板特定配置
      this.setData({
        templateConfig: templateConfig
      });
    }
  },
  
  // 只需要添加模板特有的方法
  methods: {
    // 可以添加模板特有的方法
  }
}); 