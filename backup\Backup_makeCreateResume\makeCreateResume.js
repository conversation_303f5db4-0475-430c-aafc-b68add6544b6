// 引入配置文件
const apiConfig = require('../../config/apiConfig');

Page({
  data: {
    resumeData: null,    // 初始值设为 null
    // 为每个模板定义默认主题色
    templateConfigs: {
      templateA01: {
        themeColor: '#2B6CB0',  // 商务蓝
        name: '模板一'
      },
      templateA02: {
        themeColor: '#44546B',  // 灰蓝色
        name: '模板二'
      },
      templateA03: {
        themeColor: '#2E75B6',  // 偏蓝色
        name: '模板三'
      }
    },
    config: {          
      themeColor: '#2B6CB0',
      fontSize: '11',    
      spacing: '1.2',     
      showCover: false 
    },
    template: {        
      id: 'templateA01',  // 修改为与模板组件匹配的ID       
      name: '模板一',       
      thumbnail: '/assets/images/template1.png'   
    } 
  },

  onLoad(options) {
    // 初始化云环境
    // if (!wx.cloud) {
    //   console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    //   return;
    // }
    // wx.cloud.init({
    //   env: 'jianli-6ggquwcuaee294d1', // 使用简短的环境ID
    //   traceUser: true
    // });

    console.log('========== makeCreateResume 页面开始加载 ==========');
    try {
      // // 检查组件是否存在
      // const preview = this.selectComponent('resumePreview');
      // const toolbar = this.selectComponent('toolBar');
      // const templateSelector = this.selectComponent('templateSelector');

      // console.log('组件加载状态：', {
      //   'resumePreview': !!preview,
      //   'toolBar': !!toolbar,
      //   'templateSelector': !!templateSelector
      // });

      // if (!preview || !toolbar || !templateSelector) {
      //   console.error('组件加载失败');
      //   wx.showToast({
      //     title: '页面加载异常',
      //     icon: 'none'
      //   });
      //   return;
      // }

      // 解析传递过来的数据
      if (options.resumeData) {
        try {
          const rawData = JSON.parse(decodeURIComponent(options.resumeData));
          console.log('========== makeResume传递的数据 ==========');
          console.log('模块顺序：', rawData.moduleOrders);
          
          if (rawData.moduleOrders) {
            // 确保所有模块类型都被显示
            const allModuleTypes = [
              'basicInfo', 'jobIntention', 'education', 'school', 
              'internship', 'work', 'project', 'skills', 
              'awards', 'interests', 'evaluation',
              'custom1', 'custom2', 'custom3'
            ];
            
            // 按照排序值排序并显示所有模块
            const sortedModules = allModuleTypes
              .filter(type => rawData.moduleOrders.hasOwnProperty(type))
              .sort((a, b) => rawData.moduleOrders[a] - rawData.moduleOrders[b]);
            
          //   sortedModules.forEach(moduleType => {
          //     console.log(`模块类型: ${moduleType}, 排序值: ${rawData.moduleOrders[moduleType]}`);
          //   }
          // );
          } else {
            console.log('未接收到moduleOrders数据');
          }

          // 打印所有模块数据
          // console.log('基本信息：', rawData.basicInfo);
          // console.log('求职意向：', rawData.jobIntention);
          // console.log('教育经历：', rawData.education);
          // console.log('在校经历：', rawData.school);
          // console.log('实习经历：', rawData.internship);
          // console.log('工作经历：', rawData.work);
          // console.log('项目经历：', rawData.project);
          // console.log('技能特长：', rawData.skills);
          // console.log('获奖证书：', rawData.awards);
          // console.log('兴趣爱好：', rawData.interests);
          // console.log('自我评价：', rawData.evaluation);
          // console.log('自定义模块：', rawData.custom);
          // console.log('=========================================');

          // 保存模块顺序到本地存储，以便其他组件使用
          if (rawData.moduleOrders) {
            wx.setStorageSync('makeCreateResume_moduleOrders', rawData.moduleOrders);
          }
          
          // 始终进行数据格式化，确保包含标题信息
          const resumeData = {
            moduleOrders: rawData.moduleOrders || {},  // 添加moduleOrders
            basicInfo: {
              title: '基本信息',
              ...(rawData.basicInfo || {
                name: rawData.name || '',
                gender: rawData.gender || '',
                phone: rawData.phone || '',
                photoUrl: rawData.photoUrl || ''
              })
            },
            jobIntention: {
              title: '求职意向',
              position: rawData.jobIntention?.position || '',
              city: rawData.jobIntention?.city || '',
              salary: rawData.jobIntention?.salary || '',
              status: rawData.jobIntention?.status || ''
            },
            // 数组类型的数据直接使用数组
            education: rawData.education || [],
            school: rawData.school || [],
            internship: rawData.internship || [],
            work: rawData.work || [],
            project: rawData.project || [],
            skills: rawData.skills || [],
            awards: rawData.awards || [],
            interests: rawData.interests || [],
            evaluation: rawData.evaluation || [],  // 直接使用数组格式
            custom: {
              custom1: rawData.custom?.custom1 || [],
              custom2: rawData.custom?.custom2 || [],
              custom3: rawData.custom?.custom3 || []
            }
          };

          // 打印格式化后的数据
          console.log('========== makeCreateResume格式化后的数据 ==========');
          console.log('基本信息：', resumeData.basicInfo);
          console.log('求职意向：', resumeData.jobIntention);
          console.log('教育经历：', resumeData.education);
          console.log('在校经历：', resumeData.school);
          console.log('实习经历：', resumeData.internship);
          console.log('工作经历：', resumeData.work);
          console.log('项目经历：', resumeData.project);
          console.log('技能特长：', resumeData.skills);
          console.log('获奖证书：', resumeData.awards);
          console.log('兴趣爱好：', resumeData.interests);
          console.log('自我评价：', resumeData.evaluation);
          console.log('自定义模块：', resumeData.custom);
          console.log('=========================================');

          // 确保数据完整性
          if (resumeData.basicInfo) {
            // 使用 nextTick 确保组件已完全准备好
            wx.nextTick(() => {
              this.setData({ 
                resumeData,
                'template.id': 'templateA01'  // 确保使用正确的模板ID
              }, () => {
                console.log('数据设置完成，当前页面数据：', this.data.resumeData);
                console.log('当前使用的模板：', this.data.template);
              });
            });
          } else {
            throw new Error('简历数据不完整');
          }
        } catch (error) {
          console.error('数据解析失败：', error);
          wx.showToast({
            title: '数据加载失败',
            icon: 'none'
          });
        }
      } else {
        console.warn('没有接收到简历数据');
      }

      console.log('========== 页面加载完成 ==========');
    } catch (error) {
      console.error('页面加载错误：', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }

    // console.log(options.resumeData);
  },

  // 处理模板选择
  handleTemplateSelect(e) {
    const template = e.detail;
    const templateConfig = this.data.templateConfigs[template.id];
    
    if (templateConfig) {
      wx.nextTick(() => {
        this.setData({ 
          template,
          'config.themeColor': templateConfig.themeColor
        }, () => {
          // 更新预览组件的样式
          const preview = this.selectComponent('resume-preview');
          if (preview) {
            preview.updateStyle(this.data.config);
          }
          console.log('模板更新完成：', this.data.template);
          console.log('主题色更新为：', templateConfig.themeColor);
        });
      });
    }
  },

  // 处理配置变更
  handleConfigChange(e) {
    const { field, value } = e.detail;
    console.log('配置变更：', field, value);
    
    // 使用nextTick确保数据更新和传递
    wx.nextTick(() => {
      this.setData({
        [`config.${field}`]: value
      }, () => {
        // 获取resumePreview组件
        const preview = this.selectComponent('resume-preview');
        if (preview) {
          // 强制更新resumePreview的配置
          preview.updateStyle(this.data.config);
        }
      });
    });
  },

  // // 获取模板HTML内容
  // async getTemplateHtml() {
  //   try {
  //       // 从云存储下载模板文件
  //       const { tempFilePath } = await wx.cloud.downloadFile({
  //           fileID: 'cloud://6a69-jianli-6ggquwcuaee294d1-1333175842.resumeTemplates/templateA01/preview.js'
  //       });
        
  //       console.log('tempFilePath:', tempFilePath);

  //       // 读取模板内容
  //       const templateContent = await wx.getFileSystemManager().readFileSync(tempFilePath, 'utf-8');
        
  //       // 解析模板内容
  //       const template = eval(templateContent).previewTemplate;
        
  //       // 渲染数据
  //       const renderData = {
  //           CONFIG: {
  //               themeColor: this.data.config.themeColor,
  //               fontSize: this.data.config.fontSize,
  //               spacing: this.data.config.spacing
  //           },
  //           RESUME_DATA: {
  //               ...this.data.resumeData  // 使用展开运算符包含所有数据
  //           }
  //       };

  //       // 使用Handlebars渲染模板
  //       const html = Handlebars.compile(template)(renderData);
  //       return html;
  //   } catch (error) {
  //       console.error('获取模板HTML失败:', error);
  //       return '';
  //   }
  // },

  // 获取模板CSS
  async getTemplateCSS() {
    return `
      body {
        margin: 0;
        padding: 20px;
        font-family: Arial, sans-serif;
      }
      
      .resumeTemplateA01 {
        max-width: 800px;
        margin: 0 auto;
        background: white;
        padding: 20px;
        box-shadow: 0 0 10px rgba(0,0,0,0.1);
      }
      
      .section {
        margin-bottom: 20px;
      }
      
      .section h2 {
        color: #333;
        border-bottom: 2px solid #2B6CB0;
        padding-bottom: 5px;
      }
      
      .content {
        padding: 10px 0;
      }
      
      .item {
        margin-bottom: 15px;
      }
      
      .time {
        color: #666;
        font-size: 14px;
      }
      
      .school, .company, .name {
        font-weight: bold;
        margin: 5px 0;
      }
      
      .major, .position, .role {
        color: #444;
        margin: 5px 0;
      }
      
      .description {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }
    `;
  },

  // 处理PDF生成
  async handleGeneratePDF() {
    try {
      wx.showLoading({
        title: '正在生成PDF...',
        mask: true
      });

      console.log('开始生成PDF...');
      console.log('当前配置:', this.data.config);
      console.log('简历数据:', this.data.resumeData);
      console.log('当前模板:', this.data.template);

      // 获取模板ID并处理，移除'template'前缀
      const templateId = this.data.template.id.replace('template', '');
      console.log('处理后的模板ID:', templateId);
      
      // test request
      // const testResponse = await wx.request({
      //     url: 'http://************:3000/test',
      //     method: 'GET',
      //     success: (res) => {
      //         console.log('res:', res);
      //     },
      //     fail: (error) => {
      //         console.error('testRequest失败:', error);
      //     }
      // });
      // console.log('testResponse:', testResponse);
      




      const response = await wx.request({
        // url: apiConfig.renderTemplateUrl,
        url: apiConfig.generatePDFUrl,
        method: 'POST',
        responseType: 'arraybuffer',  // 新增响应类型设置
        header: {
          'Content-Type': 'application/json'
        },
        data: {
          CONFIG: this.data.config,
          RESUME_DATA: {
            ...this.data.resumeData,
            moduleOrders: this.data.resumeData.moduleOrders || []
          },
          TEMPLATE_ID: this.data.template.id
        },
        success: (res) => {
          // 检查HTTP状态码
          if (res.statusCode !== 200) {
            try {
              const errorData = JSON.parse(String.fromCharCode(...new Uint8Array(res.data)));
              console.error('服务端返回错误:', errorData);
              wx.showToast({ title: errorData.message || '请求失败', icon: 'none' });
            } catch {
              wx.showToast({ title: `请求失败（${res.statusCode}）`, icon: 'none' });
            }
            return;
          }

          // 正常处理二进制数据
          const buffer = res.data;
          console.log('PDF文件头:', String.fromCharCode(...new Uint8Array(buffer).slice(0, 5))); // 应输出 "%PDF-"
          
          console.log('接收到的文件数据:', {
            byteLength: buffer.byteLength,
            type: typeof buffer
          });

          // 打印原始响应头
          console.log('响应头:', res.header);

          // 打印完整响应数据
          const rawData = new Uint8Array(res.data);
          console.log('原始字节:', rawData.toString('utf-8').slice(0, 20)); // 前20个字节

          // 创建一个时间戳作为文件名的一部分
          const timestamp = Date.now();
          const resumeName = wx.getStorageSync('resumeName') || 'resume';
          const safeResumeName = resumeName.replace(/[\\/:*?"<>|]/g, '_'); // 移除不安全字符

          // 使用USER_DATA_PATH作为基础路径
          const filePath = `${wx.env.USER_DATA_PATH}/${safeResumeName}_${timestamp}.pdf`;

          console.log('文件将保存到:', filePath);

          try {
            wx.getFileSystemManager().writeFile({
              filePath,
              data: buffer,
              encoding: 'binary',
              success: () => {
                console.log('文件写入成功，路径:', filePath);
                
                // 增加文件校验
                wx.getFileSystemManager().stat({
                  path: filePath,
                  success: (stat) => {
                    console.log('文件信息:', stat);
                    if (stat.stats.size > 0) {
                      // 添加PDF文件头校验
                      wx.getFileSystemManager().readFile({
                        filePath,
                        success: (fileRes) => {
                          console.log('fileRes:', fileRes);
                          const header = new Uint8Array(fileRes.data).slice(0, 5);
                          
                          console.log('header:', header);
                          console.log('fileResstring:', String.fromCharCode(...header));
                          if (String.fromCharCode(...header) === '%PDF-') {
                            wx.openDocument({
                              filePath,
                              fileType: 'pdf',
                              showMenu: true,
                              success: () => {
                                console.log('PDF打开成功');
                                wx.hideLoading();
                              },
                              fail: (error) => {
                                console.error('文件打开失败:', error);
                                wx.showToast({
                                  title: '文件打开失败，请重试',
                                  icon: 'none'
                                });
                              }
                            });
                          } else {
                            throw new Error('文件格式异常，非PDF文件');
                          }
                        }
                      });
                    } else {
                      throw new Error('生成的文件大小为0');
                    }
                  }
                });
              },
              fail: (err) => {
                console.error('文件写入失败:', {
                  error: err,
                  bufferLength: buffer.byteLength
                });
                wx.showToast({
                  title: '文件保存失败',
                  icon: 'none'
                });
              }
            });
          } catch (error) {
            console.error('文件处理失败:', error);
            throw new Error('文件处理异常');
          }
        },
        fail: (error) => {
          console.error('请求失败:', {
            statusCode: error.statusCode,
            errMsg: error.errMsg
          });
        }
      });
      wx.hideLoading();
      wx.showToast({
        title: 'PDF生成成功',
        icon: 'success',
        duration: 2000
      });

      // // 处理响应
      // console.log('response:', response);
      // console.log('response.statusCode:', response.statusCode);
      // if (response.statusCode === 200) {

      // } else {
      //     throw new Error(`请求失败，状态码：${response.statusCode}`);
      // }

      // // 获取实际的PDF生成结果
      // const pdfResult = result.result || result;
      // console.log('PDF生成结果:', pdfResult);

      // // 检查PDF生成结果
      // if (!pdfResult.success) {
      //     throw new Error(pdfResult.error || '生成PDF失败');
      // }

      // // 确保返回了必要的文件信息
      // if (!pdfResult.fileID || !pdfResult.tempUrl) {
      //     console.error('返回结果详情:', pdfResult);
      //     throw new Error('PDF文件信息不完整，请检查云函数返回结果');
      // }

      // wx.hideLoading();
      // wx.showToast({
      //     title: 'PDF生成成功',
      //     icon: 'success',
      //     duration: 2000
      // });

      // // 开始下载PDF
      // console.log('开始下载PDF...');
      // wx.showLoading({
      //     title: '准备下载...',
      //     mask: true
      // });

      // const downloadRes = await wx.downloadFile({
      //     url: pdfResult.tempUrl,
      //     success: (res) => {
      //         if (res.statusCode === 200) {
      //             // 获取用户设置的简历名称
      //             const resumeName = wx.getStorageSync('resumeName') || '我的简历';
      //             const timestamp = new Date().getTime();
      //             const newFilePath = `${wx.env.USER_DATA_PATH}/${resumeName}_${timestamp}.pdf`;

      //             try {
      //                 // 复制并重命名文件
      //                 wx.getFileSystemManager().copyFileSync(
      //                     res.tempFilePath,
      //                     newFilePath
      //                 );

      //                 // 打开文档
      //                 wx.openDocument({
      //                     filePath: newFilePath,
      //                     fileType: 'pdf',
      //                     showMenu: true,
      //                     success: () => {
      //                         wx.hideLoading();
      //                         wx.showToast({
      //                             title: '文档打开成功',
      //                             icon: 'success'
      //                         });
      //                     },
      //                     fail: (error) => {
      //                         console.error('PDF打开失败:', error);
      //                         wx.hideLoading();
      //                         wx.showToast({
      //                             title: '文档打开失败',
      //                             icon: 'none'
      //                         });
      //                         throw error;
      //                     }
      //                 });
      //             } catch (error) {
      //                 console.error('文件操作失败:', error);
      //                 wx.hideLoading();
      //                 wx.showToast({
      //                     title: '文件处理失败',
      //                     icon: 'none'
      //                 });
      //                 throw error;
      //             }
      //         } else {
      //             throw new Error('下载状态码错误: ' + res.statusCode);
      //         }
      //     },
      //     fail: (error) => {
      //         console.error('PDF下载失败:', error);
      //         wx.hideLoading();
      //         wx.showToast({
      //             title: '下载失败',
      //             icon: 'none'
      //         });
      //         throw error;
      //     }
      // });

      // return {
      //     success: true,
      //     fileID: pdfResult.fileID,
      //     tempUrl: pdfResult.tempUrl
      // };

    } catch (error) {
      wx.hideLoading();
      console.error('生成PDF过程中发生错误:', error);
      
      wx.showToast({
        title: error.message || '生成PDF失败',
        icon: 'none',
        duration: 3000
      });

      throw error;
    }
  },

  // 处理重命名事件
  handleRename(e) {
    const { name } = e.detail;
    // 将新名称保存到本地存储
    wx.setStorageSync('resumeName', name);
  }
}); 