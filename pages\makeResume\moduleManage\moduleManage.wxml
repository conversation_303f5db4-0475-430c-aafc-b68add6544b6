<!-- 模块管理页面 -->
<view class="module-manage" catchmove="{{dragging ? 'catchTouchMove' : ''}}">
  <view class="module-list">
    <!-- 固定模块：基本信息 -->
    <view class="module-item fixed-item" wx:if="{{isBasicInfoActive}}">
      <view class="module-content">
        <view class="drag-icon lock-icon">🔒</view> <!-- 可选：用锁图标表示固定 -->
        <view class="module-name">基本信息</view>
      </view>
    </view>

    <!-- 可排序模块列表 -->
    <view class="module-container">
      <view 
        wx:for="{{activeModules}}" 
        wx:key="id"
        wx:if="{{item.type !== 'basicInfo'}}"
        class="module-item {{currentIndex === index ? 'dragging' : ''}} {{dropIndex === index && currentIndex !== index ? 'drop-target' : ''}}"
        style="{{currentIndex === index ? 'transform: translateY(' + dragOffset + 'px);' : ''}}"
        data-index="{{index}}"
        bindtouchstart="handleTouchStart"
        bindtouchmove="handleTouchMove"
        bindtouchend="handleTouchEnd"
        bindtouchcancel="handleTouchEnd">
        <view class="module-content">
          <view class="drag-icon">≡</view>
          <view class="module-name">{{item.name}}</view>
        </view>
      </view>
    </view>
  </view>
  
  <view class="tip-text" wx:if="{{!isBasicInfoActive && !activeModules.length}}">暂无已填写的模块内容</view>
  <view class="save-btn" bindtap="saveModuleSettings" wx:if="{{isBasicInfoActive || activeModules.length}}">保存设置</view>
</view> 