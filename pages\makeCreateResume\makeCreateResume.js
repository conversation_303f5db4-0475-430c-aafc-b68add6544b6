// 引入API模块
const resumeApi = require('../../utils/api/resumeApi');
const app = getApp();

Page({
  data: {
    resumeData: null,    // 初始值设为 null
    // 为每个模板定义默认主题色
    templateConfigs: {
      templateA01: {
        themeColor: '#2B6CB0',  // 商务蓝
        name: '模板一'
      },
      templateA02: {
        themeColor: '#44546B',  // 灰蓝色
        name: '模板二'
      },
      templateA03: {
        themeColor: '#2E75B6',  // 偏蓝色
        name: '模板三'
      }
    },
    config: {
      themeColor: '#2B6CB0',
      fontSize: 11,
      spacing: 1.2,
      showCover: false
    },
    template: {
      id: 'templateA01',  // 修改为与模板组件匹配的ID
      name: '模板一',
      thumbnail: '/assets/images/template1.png'
    }
  },

  onLoad(options) {
    // 初始化云环境
    // if (!wx.cloud) {
    //   console.error('请使用 2.2.3 或以上的基础库以使用云能力');
    //   return;
    // }
    // wx.cloud.init({
    //   env: 'jianli-6ggquwcuaee294d1', // 使用简短的环境ID
    //   traceUser: true
    // });

    console.log('========== makeCreateResume 页面开始加载 ==========');
    try {
      // // 检查组件是否存在
      // const preview = this.selectComponent('resumePreview');
      // const toolbar = this.selectComponent('toolBar');
      // const templateSelector = this.selectComponent('templateSelector');

      // console.log('组件加载状态：', {
      //   'resumePreview': !!preview,
      //   'toolBar': !!toolbar,
      //   'templateSelector': !!templateSelector
      // });

      // if (!preview || !toolbar || !templateSelector) {
      //   console.error('组件加载失败');
      //   wx.showToast({
      //     title: '页面加载异常',
      //     icon: 'none'
      //   });
      //   return;
      // }

      // 解析传递过来的数据
      if (options.resumeData) {
        try {
          const resumeData = JSON.parse(decodeURIComponent(options.resumeData));
          console.log('========== makeResume传递的数据 ==========');
          console.log('模块顺序：', resumeData.moduleOrders);



          // 打印格式化后的数据
          console.log('========== makeCreateResume格式化后的数据 ==========');
          console.log('基本信息：', resumeData.basicInfo);
          console.log('求职意向：', resumeData.jobIntention);
          console.log('教育经历：', resumeData.education);
          console.log('在校经历：', resumeData.school);
          console.log('实习经历：', resumeData.internship);
          console.log('工作经历：', resumeData.work);
          console.log('项目经历：', resumeData.project);
          console.log('技能特长：', resumeData.skills);
          console.log('获奖证书：', resumeData.awards);
          console.log('兴趣爱好：', resumeData.interests);
          console.log('自我评价：', resumeData.evaluation);
          console.log('自定义模块：', resumeData.custom1, resumeData.custom2, resumeData.custom3);
          console.log('=========================================');

          // // 确保数据完整性
          // if (resumeData.basicInfo) {
          //   // 使用 nextTick 确保组件已完全准备好
          wx.nextTick(() => {
            this.setData({
              resumeData,
              // 'template.id': 'templateA01'  // 确保使用正确的模板ID
            }, () => {
              console.log('数据设置完成，当前页面数据：', this.data.resumeData);
              // console.log('当前使用的模板：', this.data.template);
            });
          });
          // } else {
          //   throw new Error('简历数据不完整');
          // }


        } catch (error) {
          console.error('数据解析失败：', error);
          wx.showToast({
            title: '数据加载失败',
            icon: 'none'
          });
        }
      } else {
        console.warn('没有接收到简历数据');
      }

      console.log('========== 页面加载完成 ==========');
    } catch (error) {
      console.error('页面加载错误：', error);
      wx.showToast({
        title: '页面加载失败',
        icon: 'none'
      });
    }

    // console.log(options.resumeData);
  },

  // 处理模板选择
  handleTemplateSelect(e) {
    const template = e.detail;

    // 检查是否选择了相同的模板
    if (this.data.template.id === template.id) {
      console.log(`模板 ${template.id} 未发生变化，跳过更新预览`);
      return; // 相同模板，直接返回，不更新预览
    }

    const templateConfig = this.data.templateConfigs[template.id];

    if (templateConfig) {
      wx.showLoading({
        title: '加载模板中',
        mask: true
      });

      // 先设置模板和主题色
      this.setData({
        template,
        'config.themeColor': templateConfig.themeColor
      }, () => {
        // 获取预览组件
        const preview = this.selectComponent('#resume-preview');
        if (preview) {
          // 更新样式配置（但不触发预览请求）
          preview.updateStyle(this.data.config);

          // 确保数据都已设置完毕后，通过延时执行一次性请求预览图片
          setTimeout(() => {
            // 请求服务端生成预览图片（使用自带防抖）
            preview.debounceRequestPreviewImage();

            // 记录用户行为
            if (app && app.trackUserAction) {
              app.trackUserAction('template_switch', {
                templateId: template.id,
                templateName: templateConfig.name
              });
            }

            wx.hideLoading();
            console.log('模板更新完成：', this.data.template);
            console.log('主题色更新为：', templateConfig.themeColor);
          }, 100);
        } else {
          console.error('未找到预览组件');
          wx.hideLoading();
        }
      });
    }
  },

  // 处理配置变更
  handleConfigChange(e) {
    const { field, value } = e.detail;

    // 检查是否真的发生了变化
    if (this.data.config[field] === value) {
      console.log(`配置 ${field} 未发生变化，跳过更新预览`);
      return; // 没有变化，直接返回，不更新预览
    }

    // 使用nextTick确保数据更新和传递
    wx.nextTick(() => {
      this.setData({
        [`config.${field}`]: value
      }, () => {
        // 获取resumePreview组件
        const preview = this.selectComponent('#resume-preview');
        if (preview) {
          // 显示加载提示
          if (field === 'fontSize' || field === 'spacing' || field === 'themeColor') {
            wx.showLoading({
              title: '更新预览中',
              mask: true
            });
          }

          // 强制更新resumePreview的配置
          preview.updateStyle(this.data.config);

          // 字号、行间距和主题色变更时，需要请求新的预览图片
          if (field === 'fontSize' || field === 'spacing' || field === 'themeColor') {
            // 给样式更新留出时间，然后请求预览图片
            setTimeout(() => {
              preview.debounceRequestPreviewImage();

              // 记录用户行为
              if (app && app.trackUserAction) {
                app.trackUserAction('style_change', {
                  field: field,
                  value: value
                });
              }

              wx.hideLoading();
            }, 300);
          }
        }
      });
    });
  },

  // 处理PDF生成
  async handleGeneratePDF() {
    try {
      wx.showLoading({
        title: '正在生成PDF...',
        mask: true
      });

      console.log('开始生成PDF...');
      console.log('当前配置:', this.data.config);
      console.log('简历数据:', this.data.resumeData);
      console.log('当前模板:', this.data.template);

      // 获取模板ID并处理，移除'template'前缀
      const templateId = this.data.template.id.replace('template', '');
      console.log('处理后的模板ID:', templateId);

      // test request
      // const testResponse = await wx.request({
      //     url: 'http://129.211.9.63:3000/test',
      //     method: 'GET',
      //     success: (res) => {
      //         console.log('res:', res);
      //     },
      //     fail: (error) => {
      //         console.error('testRequest失败:', error);
      //     }
      // });
      // console.log('testResponse:', testResponse);





      // 使用API模块生成PDF
      const buffer = await resumeApi.generatePDF(
        this.data.resumeData,
        this.data.config,
        this.data.template.id
      );

      console.log('PDF文件头:', String.fromCharCode(...new Uint8Array(buffer).slice(0, 5))); // 应输出 "%PDF-"

      console.log('接收到的文件数据:', {
        byteLength: buffer.byteLength,
        type: typeof buffer
      });

      // 创建一个今日日期作为文件名的一部分
      const todayDate = new Date().toISOString().split('T')[0];
      const resumeName = wx.getStorageSync('resumeName') || 'resume';
      const safeResumeName = resumeName.replace(/[\\/:*?"<>|]/g, '_'); // 移除不安全字符

      // 使用USER_DATA_PATH作为基础路径
      const filePath = `${wx.env.USER_DATA_PATH}/${safeResumeName}_${todayDate}.pdf`;

      console.log('文件将保存到:', filePath);

      // 写入文件并打开
      await new Promise((resolve, reject) => {
        wx.getFileSystemManager().writeFile({
          filePath,
          data: buffer,
          encoding: 'binary',
          success: () => {
            console.log('文件写入成功，路径:', filePath);

            // 增加文件校验
            wx.getFileSystemManager().stat({
              path: filePath,
              success: (stat) => {
                console.log('文件信息:', stat);
                if (stat.stats.size > 0) {
                  // 添加PDF文件头校验
                  wx.getFileSystemManager().readFile({
                    filePath,
                    success: (fileRes) => {
                      console.log('fileRes:', fileRes);
                      const header = new Uint8Array(fileRes.data).slice(0, 5);

                      console.log('header:', header);
                      console.log('fileResstring:', String.fromCharCode(...header));
                      if (String.fromCharCode(...header) === '%PDF-') {
                        wx.openDocument({
                          filePath,
                          fileType: 'pdf',
                          showMenu: true,
                          success: () => {
                            console.log('PDF打开成功');
                            resolve();
                          },
                          fail: (error) => {
                            console.error('文件打开失败:', error);
                            wx.showToast({
                              title: '文件打开失败，请重试',
                              icon: 'none'
                            });
                            reject(error);
                          }
                        });
                      } else {
                        const error = new Error('文件格式异常，非PDF文件');
                        reject(error);
                      }
                    },
                    fail: reject
                  });
                } else {
                  const error = new Error('生成的文件大小为0');
                  reject(error);
                }
              },
              fail: reject
            });
          },
          fail: (err) => {
            console.error('文件写入失败:', {
              error: err,
              bufferLength: buffer.byteLength
            });
            wx.showToast({
              title: '文件保存失败',
              icon: 'none'
            });
            reject(err);
          }
        });
      });



      wx.showToast({
        title: 'PDF生成成功',
        icon: 'success',
        duration: 2000
      });

    } catch (error) {
      wx.hideLoading();
      console.error('生成PDF过程中发生错误:', error);

      wx.showToast({
        title: error.message || '生成PDF失败',
        icon: 'none',
        duration: 3000
      });

      throw error;
    }
  },

  // 处理重命名事件
  handleRename(e) {
    const { name } = e.detail;
    // 将新名称保存到本地存储
    wx.setStorageSync('resumeName', name);
  }
});