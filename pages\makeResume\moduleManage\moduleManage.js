Page({
  data: {
    modules: [
      { id: 1, type: 'basicInfo', name: '基本信息'},
      { id: 2, type: 'jobIntention', name: '求职意向'},
      { id: 3, type: 'education', name: '教育经历'},
      { id: 4, type: 'school', name: '在校经历'},
      { id: 5, type: 'internship', name: '实习经历'},
      { id: 6, type: 'work', name: '工作经历'},
      { id: 7, type: 'project', name: '项目经历'},
      { id: 8, type: 'skills', name: '技能特长'},
      { id: 9, type: 'awards', name: '奖项证书'},
      { id: 10, type: 'interests', name: '兴趣爱好'},
      { id: 11, type: 'evaluation', name: '自我评价'},
      { id: 12, type: 'custom1', name: '自定义名称一'},
      { id: 13, type: 'custom2', name: '自定义名称二'},
      { id: 14, type: 'custom3', name: '自定义名称三'}
    ],
    activeModules: [], // 存储已填写数据的模块 (不含 basicInfo)
    moduleOrders: {},
    isBasicInfoActive: false, // 基本信息模块是否激活
    itemHeight: 45,    // 每个模块的高度(px)
    
    // 拖拽相关
    currentIndex: -1,     // 当前拖拽的模块索引
    startY: 0,            // 拖拽开始时的Y坐标
    moveY: 0,             // 当前移动的Y坐标
    dragging: false,      // 是否正在拖拽
    dragOffset: 0,        // 拖拽偏移量
    dropIndex: -1,        // 最终释放的索引位置
    lastMoveTime: 0       // 上次移动时间，用于防抖
  },

  onLoad: function(options) {
    // 加载已填写数据的模块
    // this.loadActiveModules();
    const activeModules = wx.getStorageSync('activeModules');
    this.setData({
      activeModules
    });
    const moduleOrders = wx.getStorageSync('moduleOrders');
    this.setData({
      moduleOrders
    });

    // 判断basicInfo是否存在  
    if (activeModules.find(module => module.type === 'basicInfo')) {
      this.setData({
        isBasicInfoActive: true
      });
    }
  },

  onReady: function() {
    // 页面渲染完成后处理
    console.log('页面渲染完成');
    
    // 获取元素高度
    this.getItemHeight();
  },
  
  // 获取模块元素的实际高度
  getItemHeight: function() {
    const query = wx.createSelectorQuery().in(this);
    query.select('.module-item').boundingClientRect();
    query.exec(res => {
      if (res && res[0]) {
        this.setData({
          itemHeight: res[0].height || 45
        });
        console.log('模块实际高度:', res[0].height, 'px');
      }
    });
  },

  // 加载已填写数据的模块
  // loadActiveModules: function() {
  //   console.log('开始加载模块数据...');
    
  //   // 获取基本信息
  //   const basicInfo = wx.getStorageSync('basicInfo') || {};
  //   const jobIntention = wx.getStorageSync('jobIntention') || {};
  //   const education = wx.getStorageSync('education') || [];
  //   const schoolList = wx.getStorageSync('schoolList') || [];
  //   const internshipList = wx.getStorageSync('internshipList') || [];
  //   const workList = wx.getStorageSync('workList') || [];
  //   const projectList = wx.getStorageSync('projectList') || [];
  //   const skillsList = wx.getStorageSync('skillsList') || [];
  //   const awardsList = wx.getStorageSync('awardsList') || [];
  //   const interestsList = wx.getStorageSync('interestsList') || [];
  //   const evaluationList = wx.getStorageSync('evaluationList') || [];
    
  //   // 获取自定义模块数据 - 兼容两种存储格式
  //   const customList1 = wx.getStorageSync('customList1') || [];
  //   const customList2 = wx.getStorageSync('customList2') || [];
  //   const customList3 = wx.getStorageSync('customList3') || [];

  //   // 检查每个模块是否有数据
  //   const activeModules = JSON.parse(JSON.stringify(this.data.modules))
  //     // .filter(module => module.type !== 'basicInfo') // 排除 basicInfo
  //     .filter(module => { // 对剩余模块进行内容检查

  //     // console.log('函数开始时的activeModules:', activeModules);

  //     let hasContent = false;
      
  //     switch(module.type) {
  //       case 'basicInfo':
  //         // 基本信息至少需要姓名或电话
  //         hasContent = !!(basicInfo.name || basicInfo.phone);
  //         break;
          
  //       case 'jobIntention':
  //         // 求职意向需要至少有一个字段有内容
  //         hasContent = !!(jobIntention && (
  //           jobIntention.position || 
  //           jobIntention.salary || 
  //           jobIntention.location || 
  //           jobIntention.status
  //         ));
  //         break;
          
  //       case 'education':
  //         // 教育经历需要有数组且不为空
  //         hasContent = education && education.length > 0;
  //         break;
          
  //       case 'school':
  //         // 在校经历需要有数组且不为空
  //         hasContent = schoolList && schoolList.length > 0;
  //         break;
          
  //       case 'internship':
  //         // 实习经历需要有数组且不为空
  //         hasContent = internshipList && internshipList.length > 0;
  //         break;
          
  //       case 'work':
  //         // 工作经历需要有数组且不为空
  //         hasContent = workList && workList.length > 0;
  //         break;
          
  //       case 'project':
  //         // 项目经历需要有数组且不为空
  //         hasContent = projectList && projectList.length > 0;
  //         break;
          
  //       case 'skills':
  //         // 技能特长必须有数组且至少一个有内容的元素
  //         hasContent = skillsList && skillsList.length > 0 && 
  //           skillsList.some(item => typeof item === 'string' ? item.trim() !== '' : false);
  //         break;
          
  //       case 'awards':
  //         // 奖项证书必须有数组且至少一个有内容的元素
  //         hasContent = awardsList && awardsList.length > 0 && 
  //           awardsList.some(item => typeof item === 'string' ? item.trim() !== '' : false);
  //         break;
          
  //       case 'interests':
  //         // 兴趣爱好必须有数组且至少一个有内容的元素
  //         hasContent = interestsList && interestsList.length > 0 && 
  //           interestsList.some(item => typeof item === 'string' ? item.trim() !== '' : false);
  //         break;
          
  //       case 'evaluation':
  //         // 自我评价必须有数组且至少一个有内容的content字段
  //         hasContent = evaluationList && evaluationList.length > 0 && 
  //           evaluationList.some(item => item && item.content && item.content.trim() !== '');
  //         break;
          
  //       case 'custom1':
  //         // 自定义模块一必须有数组且至少一个有内容的字段
  //         hasContent = customList1 && customList1.length > 0 
  //         && 
  //           customList1.some(item => item && (
  //             (item.customName && item.customName.trim() !== '') || 
  //             (item.content && item.content.trim() !== '') || 
  //             (item.role && item.role.trim() !== '')
  //           ));
  //         break;
          
  //       case 'custom2':
  //         // 自定义模块二必须有数组且至少一个有内容的字段
  //         hasContent = customList2 && customList2.length > 0 && 
  //           customList2.some(item => item && (
  //             (item.customName && item.customName.trim() !== '') || 
  //             (item.content && item.content.trim() !== '') || 
  //             (item.role && item.role.trim() !== '')
  //           ));
  //         break;
          
  //       case 'custom3':
  //         // 自定义模块三必须有数组且至少一个有内容的字段
  //         hasContent = customList3 && customList3.length > 0 && 
  //           customList3.some(item => item && (
  //             (item.customName && item.customName.trim() !== '') || 
  //             (item.content && item.content.trim() !== '') || 
  //             (item.role && item.role.trim() !== '')
  //           ));
  //         break;
  //     }
      
  //     return hasContent;
  //   });

  //   console.log('过滤后的活动模块:', activeModules);

  //   // 加载已保存的顺序 (只针对 activeModules)
  //   const moduleSettings = wx.getStorageSync('moduleSettings');
  //   const moduleOrders = wx.getStorageSync('moduleOrders') || {};
    
  //   if (moduleSettings && moduleSettings.length > 0) {
  //     console.log('已存在模块设置', moduleSettings);
  //     activeModules.forEach(module => {
  //       const setting = moduleSettings.find(m => m.type === module.type);
  //       // 注意：排序值需要减1，因为 basicInfo 占用了 0
  //       if (setting && setting.order > 0) { 
  //         module.order = setting.order;
  //       } else if (moduleOrders[module.type] !== undefined && moduleOrders[module.type] > 0) {
  //         module.order = moduleOrders[module.type];
  //       } else {
  //          // 如果没有有效排序值，则临时给一个大值，保证排在后面
  //          module.order = activeModules.length + (module.id || 0); 
  //       }
  //     });
  //   } else {
  //     console.log('无已存模块设置，使用默认顺序（从1开始）');
  //     activeModules.forEach((module, index) => {
  //       module.order = index + 1; // 排序从 1 开始
  //       // module.order = index;
  //     });
  //   }
    
  //   // 按order排序
  //   activeModules.sort((a, b) => a.order - b.order);
    
  //   console.log('排序后的可排序模块顺序:', activeModules.map(m => m.name));

  //   // 更新数据
  //   this.setData({ 
  //     activeModules // 只设置过滤和排序后的非 basicInfo 模块
  //   });
  // },

  // 触摸开始
  handleTouchStart: function(e) {
    // 获取当前拖拽的模块索引
    const index = e.currentTarget.dataset.index;
    
    console.log('触摸开始', index);
    
    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY,
      moveY: e.touches[0].clientY,
      dragging: true,
      dragOffset: 0,
      dropIndex: index,
      lastMoveTime: Date.now()
    });
  },

  // 触摸移动
  handleTouchMove: function(e) {
    const { dragging, currentIndex, lastMoveTime, startY } = this.data;
    
    if (!dragging) return;
    
    // 简单的防抖处理
    const now = Date.now();
    if (now - lastMoveTime < 32) { // 约30fps
      return;
    }
    
    const moveY = e.touches[0].clientY;
    // const startY = this.data.startY;
    const dragOffset = moveY - startY;
    const activeModulesCount = this.data.activeModules.length;
    
    // 计算目标位置索引
    const itemHeight = this.data.itemHeight;
    let offsetIndex = Math.round(dragOffset / itemHeight);
    let targetIndex = currentIndex + offsetIndex;
    
    // 限制目标索引在有效范围内
    targetIndex = Math.max(1, Math.min(targetIndex, activeModulesCount - 1)); // 保证basicInfo 在index 0

    // 更新数据
    this.setData({
      moveY: moveY,
      dragOffset: dragOffset,
      dropIndex: targetIndex,
      lastMoveTime: now
    });
  },

  // 触摸结束
  handleTouchEnd: function() {
    const { dragging, currentIndex, dropIndex, dragOffset } = this.data; // 保留 dragOffset

    if (!dragging) return;

    // 先重置 dragging 状态，移除视觉效果，但保留 transform
    this.setData({
      dragging: false,
    });

    if (currentIndex !== dropIndex && dropIndex !== -1) {
      // 执行实际排序（会触发 WXML 重新渲染）
      this.moveItem(currentIndex, dropIndex);

      // 使用 nextTick 确保在 WXML 更新后才重置视觉偏移
      // 这会触发 CSS transition，让元素平滑落到新位置
      wx.nextTick(() => {
        this.setData({
          currentIndex: -1,
          dragOffset: 0,
          dropIndex: -1
        });
      });
    } else {
      // 如果没有移动或移动无效，则直接平滑动画回原位
      this.setData({
        currentIndex: -1,
        dragOffset: 0,
        dropIndex: -1
      });
    }
    
    console.log('拖拽结束');
  },
  
  // 移动项目到目标位置
  moveItem: function(fromIndex, toIndex) {
    console.log(`移动模块：从 ${fromIndex} 到 ${toIndex}`);
    
    if (fromIndex === toIndex) return;
    
    const activeModules = this.data.activeModules;

    const currentItem = activeModules.splice(fromIndex, 1)[0];

    activeModules.splice(toIndex, 0, currentItem);

    this.setData({
      activeModules: activeModules
    });
  },

  // 保存模块设置
  saveModuleSettings: function() {
    const activeModules = this.data.activeModules;
    
    // 确保 basicInfo 在 index 0
    const indexBasicInfo = activeModules.findIndex(module => module.type === 'basicInfo');
    if (indexBasicInfo !== 0) {
      activeModules.splice(indexBasicInfo, 1);
      activeModules.unshift({ type: 'basicInfo', name: '基本信息' });
    }
    const latestModuleOrderSettings = {};
    activeModules.forEach((module, index) => {
      latestModuleOrderSettings[module.type] = index;
    });

    wx.setStorageSync('activeModules', activeModules);
    wx.setStorageSync('moduleOrders', latestModuleOrderSettings);
    console.log('保存的activeModules:', activeModules);
    console.log('保存的moduleOrders:', latestModuleOrderSettings);
    // 显示成功提示并返回
    wx.showToast({ title: '保存成功', icon: 'success', duration: 500 });
    setTimeout(() => { wx.navigateBack(); }, 500);
  }
}); 