Page({
  data: {
    projectFormData: [],
    currentIndex: -1,
    startY: 0,
    moveY: 0
  },

  onLoad() {
    this.loadProjectData()
  },

  onShow() {
    this.loadProjectData()
  },

  loadProjectData() {
    const projectData = wx.getStorageSync('projectList') || []
    this.setData({
      projectFormData: projectData
    })
  },

  addProject() {
    wx.navigateTo({
      url: '../projectEdit/projectEdit'
    })
  },

  editProject(e) {
    const index = e.currentTarget.dataset.index
    wx.navigateTo({
      url: `../projectEdit/projectEdit?index=${index}`
    })
  },

  deleteProject(e) {
    const index = e.currentTarget.dataset.index
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这条项目经历吗？',
      success: (res) => {
        if (res.confirm) {
          let projectData = this.data.projectFormData
          projectData.splice(index, 1)
          wx.setStorageSync('projectList', projectData)
          this.setData({
            projectFormData: projectData
          })
          wx.showToast({
            title: '删除成功',
            icon: 'success'
          })
        }
      }
    })
  },

  // 长按触发移动
  handleLongPress(e) {
    const index = e.currentTarget.dataset.index
    this.setData({
      currentIndex: index,
      startY: e.touches[0].clientY
    })
    wx.vibrateShort() // 添加震动反馈
  },

  // 触摸移动中
  touchMove(e) {
    if (this.data.currentIndex < 0) return
    
    const moveY = e.touches[0].clientY
    const moveDistance = moveY - this.data.startY
    
    // 计算目标位置
    const itemHeight = 50 // 每个项目的高度，根据实际调整
    const moveIndex = Math.round(moveDistance / itemHeight)
    
    let targetIndex = this.data.currentIndex + moveIndex
    targetIndex = Math.max(0, Math.min(targetIndex, this.data.projectFormData.length - 1))
    
    if (targetIndex !== this.data.currentIndex) {
      // 交换位置
      const projectFormData = [...this.data.projectFormData]
      const temp = projectFormData[this.data.currentIndex]
      projectFormData[this.data.currentIndex] = projectFormData[targetIndex]
      projectFormData[targetIndex] = temp
      
      this.setData({
        projectFormData,
        currentIndex: targetIndex,
        startY: moveY
      })
    }
  },

  // 触摸结束
  touchEnd() {
    if (this.data.currentIndex >= 0) {
      // 更新所有项的sortIndex
      const projectFormData = this.data.projectFormData.map((item, index) => ({
        ...item,
        sortIndex: index
      }))
      
      // 保存排序后的数据
      this.setData({
        projectFormData,
        currentIndex: -1,
        startY: 0,
        moveY: 0
      })
      
      // 更新存储
      wx.setStorageSync('projectList', projectFormData)

      // 添加排序成功的提示
      wx.showToast({
        title: '排序成功',
        icon: 'success',
        duration: 1500
      })
    }
  }
}) 