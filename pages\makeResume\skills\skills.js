Page({
  data: {
    skillsList: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']  // 16个空字符串
  },

  onLoad() {
    // 加载已保存的数据
    const skillsList = wx.getStorageSync('skillsList') || [];
    if (skillsList.length > 0) {
      // 将已有数据填充到16个空位中
      const newSkillsList = [...this.data.skillsList];
      skillsList.forEach((skill, index) => {
        if (index < 16) {
          newSkillsList[index] = skill;
        }
      });

      this.setData({
        skillsList: newSkillsList
      });
    }
  },

  // 输入框内容变化时触发
  handleInput(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;

    const skillsList = [...this.data.skillsList];
    skillsList[index] = value;

    this.setData({
      skillsList: skillsList
    });
  },

  // 保存信息
  saveInfo() {
    // 过滤掉空字符串，并确保每个项都是字符串
    const skillsList = this.data.skillsList
      .map(item => {
        if (typeof item === 'object' && item !== null) {
          return item.content || '';
        }
        return item;
      })
      .filter(item => typeof item === 'string' && item.trim() !== '');

    // 直接保存到本地存储
    wx.setStorageSync('skillsList', skillsList);

    // 同时保存到resumeManager
    const resumeManager = require('../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新技能特长
    currentResumeData.skills = skillsList;

    // 保存到resumeManager
    resumeManager.saveCurrentResumeData(currentResumeData);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1500,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      }
    });
  },

  // 删除信息
  deleteInfo() {
    wx.showModal({
      title: '提示',
      content: '确定要删除所有技能特长吗？',
      success: (res) => {
        if (res.confirm) {
          this.setData({
            skillsList: ['', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '']
          });

          wx.setStorageSync('skillsList', []);

          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 1500,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            }
          });
        }
      }
    });
  }
});