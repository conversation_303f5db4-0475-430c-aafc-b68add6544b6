# 微信小程序简历生成器开发文档

## 项目概述
本程序是微信小程序，主要功能是将用户输入的简历数据发送到服务器，然后服务器将简历数据转换成JPEG或PDF格式的文件。

## 开发环境
- 开发语言：JavaScript
- 开发框架：微信小程序框架
- 开发工具：微信开发者工具
- 开发环境：Windows 10
- 命令行终端：PowerShell

## 当前任务清单

### 简历模板预览修复 (2024-01-XX)
[√] 1. 修改预览图片文件命名策略，包含模板ID和时间戳
[√] 2. 在模板切换时清理旧的预览图片文件
[√] 3. 添加图片URL的缓存破坏参数
[√] 4. 优化预览图片的加载和显示逻辑
[√] 5. 添加调试日志便于问题排查
[ ] 6. 测试修复效果

### 问题描述
用户反馈：在微信端简历预览界面，切换任何一个简历模板时，得到的预览图始终是2号模板的。

### 问题分析
1. **临时文件路径固定**：预览图片使用固定的文件路径，导致新图片覆盖旧图片
2. **缓存问题**：微信小程序的图片缓存机制导致显示旧图片
3. **文件清理不足**：没有有效的缓存清理机制

### 已实现功能
[√] 用户登录和状态管理
[√] 简历数据输入和编辑
[√] 多种简历模板支持
[√] PDF文件生成和下载
[√] 简历预览功能（基础版本）
[√] 主题色和样式配置

### 未实现功能
[ ] 简历模板切换预览图正确显示（修复中）
[ ] 多份简历数据管理
[ ] 全局一键分享功能
[ ] 反馈功能
[ ] 数据分析功能

### 技术架构
- 前端：微信小程序
- 后端：Node.js服务器
- 数据存储：本地存储 + 服务器存储
- 文件生成：服务器端PDF/JPEG生成

## 接口文档

### 简历预览图片生成接口
- **URL**: `/resume/export-jpeg`
- **方法**: POST
- **参数**:
  - `resume_data`: 简历数据对象
  - `theme_config`: 主题配置对象
  - `template_id`: 模板ID字符串
- **返回**: JPEG格式的二进制图片数据

### 简历PDF生成接口
- **URL**: `/resume/export-pdf`
- **方法**: POST
- **参数**:
  - `resume_data`: 简历数据对象
  - `theme_config`: 主题配置对象
  - `template_id`: 模板ID字符串
- **返回**: PDF格式的二进制文件数据
