Page({
  data: {
    schoolFormData: [],
    sortable: false,        // 是否在排序状态
    currentIndex: -1,       // 当前拖动项的索引
    startY: 0,             // 开始触摸的纵坐标
    itemHeight: 0          // 每个项目的高度
  },

  onLoad() {
    // 加载已保存的数据
    const schoolList = wx.getStorageSync('schoolList') || [];
    const schoolFormData = schoolList.map((item, index) => {
      return {
        ...item,
        sortIndex: index
      };
    });
    this.setData({
      schoolFormData
    });
  },

  // 长按开始排序
  handleLongPress(e) {
    // 获取元素高度
    const query = wx.createSelectorQuery();
    query.select('.schoolItem').boundingClientRect();
    query.exec((res) => {
      if (res[0]) {
        this.setData({
          sortable: true,
          currentIndex: e.currentTarget.dataset.index,
          startY: e.touches[0].clientY,
          itemHeight: res[0].height
        });
      }
    });
  },

  // 触摸移动
  touchMove(e) {
    if (!this.data.sortable || !this.data.itemHeight) return;

    const moveY = e.touches[0].clientY;
    const diffY = moveY - this.data.startY;
    const moveIndex = Math.round(diffY / this.data.itemHeight);
    let currentIndex = this.data.currentIndex;
    let targetIndex = currentIndex + moveIndex;

    // 确保目标索引在有效范围内
    if (targetIndex < 0) {
      targetIndex = 0;
    } else if (targetIndex >= this.data.schoolFormData.length) {
      targetIndex = this.data.schoolFormData.length - 1;
    }

    if (targetIndex !== currentIndex) {
      // 交换位置
      let schoolFormData = [...this.data.schoolFormData];  // 创建数组副本
      const temp = schoolFormData[currentIndex];
      schoolFormData[currentIndex] = schoolFormData[targetIndex];
      schoolFormData[targetIndex] = temp;

      // 更新排序索引
      schoolFormData = schoolFormData.map((item, index) => ({
        ...item,
        sortIndex: index
      }));

      this.setData({
        schoolFormData,
        currentIndex: targetIndex,
        startY: moveY
      });
    }
  },

  // 触摸结束
  touchEnd() {
    if (!this.data.sortable) return;

    this.setData({
      sortable: false,
      currentIndex: -1,
      itemHeight: 0
    });

    // 保存排序后的结果
    this.saveData();
  },

  // 添加在校经历
  addSchool() {
    wx.navigateTo({
      url: '../schoolEdit/schoolEdit'
    });
  },

  // 编辑在校经历
  editSchool(e) {
    if (this.data.sortable) return; // 排序状态下不允许编辑
    const index = e.currentTarget.dataset.index;
    wx.navigateTo({
      url: `../schoolEdit/schoolEdit?index=${index}`
    });
  },

  // 删除在校经历
  deleteSchool(e) {
    if (this.data.sortable) return; // 排序状态下不允许删除
    const index = e.currentTarget.dataset.index;
    wx.showModal({
      title: '提示',
      content: '确定要删除这条在校经历吗？',
      success: (res) => {
        if (res.confirm) {
          let schoolFormData = [...this.data.schoolFormData];  // 创建数组副本
          schoolFormData.splice(index, 1);

          // 更新排序索引
          schoolFormData = schoolFormData.map((item, index) => ({
            ...item,
            sortIndex: index
          }));

          this.setData({ schoolFormData });
          this.saveData();

          // 同时同步到resumeManager
          const resumeManager = require('../../../../utils/resume/resumeManager.js');
          const currentResumeData = resumeManager.getCurrentResumeData() || {};
          currentResumeData.schools = schoolFormData;
          resumeManager.saveCurrentResumeData(currentResumeData);

          console.log('=== 在校经历删除同步 ===');
          console.log('删除后的在校经历数据:', schoolFormData);
        }
      }
    });
  },

  // 保存数据到本地存储
  saveData() {
    // 直接保存到schoolList键
    wx.setStorageSync('schoolList', this.data.schoolFormData);
  },

  // 页面显示时刷新数据
  onShow() {
    const schoolList = wx.getStorageSync('schoolList') || [];
    // 确保数据按sortIndex排序并且每项都有sortIndex
    const schoolFormData = schoolList
      .sort((a, b) => (a.sortIndex || 0) - (b.sortIndex || 0))
      .map((item, index) => ({
        ...item,
        sortIndex: index
      }));

    this.setData({ schoolFormData });
  }
});