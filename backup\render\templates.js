/**
 * 简历模板集合
 * 每个模板包含HTML结构和CSS样式
 */
const resumeTemplates = {
  // 模板03 - 左右布局简历
  template03: {
    html: `
    <div class="resume-container" id="{{containerId}}">
        <!-- 左侧栏 -->
        <div class="left-column">
            <!-- 照片区域 -->
            <div class="photo-container">
                <img id="avatar" src="{{avatar}}" alt="个人照片">
            </div>
            
            <!-- 姓名 -->
            <div class="name-container">
                <h1 id="name">{{name}}</h1>
            </div>
            
            <!-- 联系方式 -->
            <div class="section {{hideBasicInfo}}" id="contact-section">
                <div class="section-title">
                    <i class="fas fa-phone-alt"></i>
                    <h2>联系方式</h2>
                </div>
                <div class="section-content">
                    <p><span>电话：</span><span id="phone">{{phone}}</span></p>
                    <p><span>邮箱：</span><span id="email">{{email}}</span></p>
                    <p><span>微信：</span><span id="wechat">{{wechat}}</span></p>
                </div>
            </div>
            
            <!-- 个人信息 -->
            <div class="section {{hidePersonalInfo}}" id="personal-info-section">
                <div class="section-title">
                    <i class="fas fa-user"></i>
                    <h2>个人信息</h2>
                </div>
                <div class="section-content">
                    <p><span>性别：</span><span id="gender">{{gender}}</span></p>
                    <p><span>年龄：</span><span id="age">{{age}}</span></p>
                    <p><span>民族：</span><span id="ethnicity">{{ethnicity}}</span></p>
                    <p><span>籍贯：</span><span id="hometown">{{hometown}}</span></p>
                    <p><span>现居：</span><span id="current-location">{{currentLocation}}</span></p>
                    <p><span>婚姻状况：</span><span id="marriage">{{marriage}}</span></p>
                    <p><span>生日：</span><span id="birthday">{{birthday}}</span></p>
                    <p><span>身高：</span><span id="height">{{height}}</span></p>
                    <p><span>体重：</span><span id="weight">{{weight}}</span></p>
                    <p><span>学历：</span><span id="education-level">{{educationLevel}}</span></p>
                    <p><span>政治面貌：</span><span id="political-status">{{politicalStatus}}</span></p>
                </div>
            </div>
        </div>
        
        <!-- 右侧栏 -->
        <div class="right-column">
            <!-- 求职意向 -->
            <div class="section {{hideJobIntention}}" id="job-intention-section">
                <div class="section-title">
                    <i class="fas fa-bullseye"></i>
                    <h2>求职意向</h2>
                </div>
                <div class="section-content">
                    <p><span>期望职位：</span><span id="position">{{position}}</span></p>
                    <p><span>期望薪资：</span><span id="salary">{{salary}}</span></p>
                    <p><span>期望城市：</span><span id="job-city">{{jobCity}}</span></p>
                    <p><span>求职状态：</span><span id="job-status">{{jobStatus}}</span></p>
                </div>
            </div>
            
            <!-- 教育背景 -->
            <div class="section {{hideEducation}}" id="education-section">
                <div class="section-title">
                    <i class="fas fa-graduation-cap"></i>
                    <h2>教育背景</h2>
                </div>
                <div class="section-content" id="education-content">
                    {{educationHTML}}
                </div>
            </div>
            
            <!-- 工作经历 -->
            <div class="section {{hideWork}}" id="work-section">
                <div class="section-title">
                    <i class="fas fa-briefcase"></i>
                    <h2>工作经历</h2>
                </div>
                <div class="section-content" id="work-content">
                    {{workHTML}}
                </div>
            </div>
            
            <!-- 项目经历 -->
            <div class="section {{hideProject}}" id="project-section">
                <div class="section-title">
                    <i class="fas fa-project-diagram"></i>
                    <h2>项目经历</h2>
                </div>
                <div class="section-content" id="project-content">
                    {{projectHTML}}
                </div>
            </div>
            
            <!-- 技能证书 -->
            <div class="section {{hideSkills}}" id="skills-section">
                <div class="section-title">
                    <i class="fas fa-tools"></i>
                    <h2>技能证书</h2>
                </div>
                <div class="section-content" id="skills-content">
                    {{skillsHTML}}
                </div>
            </div>
            
            <!-- 奖项荣誉 -->
            <div class="section {{hideAwards}}" id="awards-section">
                <div class="section-title">
                    <i class="fas fa-award"></i>
                    <h2>奖项荣誉</h2>
                </div>
                <div class="section-content" id="awards-content">
                    {{awardsHTML}}
                </div>
            </div>
            
            <!-- 兴趣爱好 -->
            <div class="section {{hideInterests}}" id="interests-section">
                <div class="section-title">
                    <i class="fas fa-heart"></i>
                    <h2>兴趣爱好</h2>
                </div>
                <div class="section-content" id="interests-content">
                    {{interestsHTML}}
                </div>
            </div>
            
            <!-- 自我评价 -->
            <div class="section {{hideEvaluation}}" id="self-evaluation-section">
                <div class="section-title">
                    <i class="fas fa-comment"></i>
                    <h2>自我评价</h2>
                </div>
                <div class="section-content" id="self-evaluation-content">
                    {{evaluationHTML}}
                </div>
            </div>
            
            <!-- 自定义模块1 -->
            <div class="section {{hideCustom1}}" id="custom1-section">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>{{custom1.title}}</h2>
                </div>
                <div class="section-content">
                    {{custom1HTML}}
                </div>
            </div>
            
            <!-- 自定义模块2 -->
            <div class="section {{hideCustom2}}" id="custom2-section">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>{{custom2.title}}</h2>
                </div>
                <div class="section-content">
                    {{custom2HTML}}
                </div>
            </div>
            
            <!-- 自定义模块3 -->
            <div class="section {{hideCustom3}}" id="custom3-section">
                <div class="section-title">
                    <i class="fas fa-star"></i>
                    <h2>{{custom3.title}}</h2>
                </div>
                <div class="section-content">
                    {{custom3HTML}}
                </div>
            </div>
        </div>
    </div>
    `,
    css: `/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "Microsoft YaHei", "SimHei", sans-serif;
}

body {
    background-color: #f5f5f5;
    display: flex;
    justify-content: center;
    padding: 20px;
}

.resume-container {
    width: 210mm;
    min-height: 297mm;
    background-color: white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    display: flex;
}

/* 左侧栏样式 */
.left-column {
    width: 30%;
    background-color: #f0f0f0;
    padding-bottom: 20px;
}

.photo-container {
    padding: 20px;
    display: flex;
    justify-content: center;
}

.photo-container img {
    width: 150px;
    height: 200px;
    object-fit: cover;
    border: 5px solid white;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}

.name-container {
    text-align: center;
    padding: 10px 20px 20px;
}

.name-container h1 {
    font-size: 24px;
    color: #333;
}

/* 右侧栏样式 */
.right-column {
    width: 70%;
    padding: 20px;
}

/* 通用部分样式 */
.section {
    margin-bottom: 20px;
}

.section-title {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    border-bottom: 2px solid #3a4a5a;
    padding-bottom: 5px;
}

.section-title i {
    margin-right: 10px;
    color: #3a4a5a;
    font-size: 18px;
}

.section-title h2 {
    font-size: 18px;
    color: #3a4a5a;
}

.section-content {
    padding-left: 5px;
}

.section-content p {
    margin-bottom: 5px;
    line-height: 1.5;
}

.section-content span:first-child {
    font-weight: bold;
    display: inline-block;
    width: 80px;
}

/* 经历项目样式 */
.experience-item {
    position: relative;
    padding-left: 15px;
    margin-bottom: 10px;
}

.experience-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #3a4a5a;
    border-radius: 50%;
}

/* 评价项目样式 */
.evaluation-item {
    position: relative;
    padding-left: 15px;
    margin-bottom: 10px;
}

.evaluation-item:before {
    content: "";
    position: absolute;
    left: 0;
    top: 5px;
    width: 8px;
    height: 8px;
    background-color: #3a4a5a;
    border-radius: 50%;
}

/* 隐藏空模块 */
.hidden {
    display: none;
}
    `,
    // 定义模板特定的组件渲染函数
    renderEducation: function(education) {
      if (!education || education.length === 0) return '';
      
      return education.map((edu) => `
        <div class="education-item">
          <p><strong>${edu.schoolName || ''}</strong> ${edu.startDate || ''} - ${edu.endDate || ''}</p>
          <p>专业：${edu.major || ''}</p>
          <p>学历：${edu.education || ''}</p>
          <p>主修课程：${edu.courses || ''}</p>
        </div>
      `).join('');
    },
    
    renderWork: function(work) {
      if (!work || work.length === 0) return '';
      
      return work.map((job) => `
        <div class="work-item">
          <p><strong>${job.companyName || ''}</strong> ${job.startDate || ''} - ${job.endDate || ''}</p>
          <p>职位：${job.jobTitle || ''}</p>
          <p>工作内容：${job.jobContent || ''}</p>
        </div>
      `).join('');
    },
    
    renderProject: function(projects) {
      if (!projects || projects.length === 0) return '';
      
      return projects.map((project) => `
        <div class="project-item">
          <p><strong>${project.projectName || ''}</strong> ${project.startDate || ''} - ${project.endDate || ''}</p>
          <p>角色：${project.role || ''}</p>
          <p>项目描述：${project.projectDesc || ''}</p>
        </div>
      `).join('');
    },
    
    renderSkills: function(skills) {
      if (!skills || skills.length === 0) return '';
      
      return skills.map((skill) => `
        <div class="skill-item">
          <p>${skill.skillName || ''}: ${skill.skillLevel || ''}</p>
          <p>${skill.skillDesc || ''}</p>
        </div>
      `).join('');
    },
    
    renderAwards: function(awards) {
      if (!awards || awards.length === 0) return '';
      
      return awards.map((award) => `
        <div class="award-item">
          <p><strong>${award.awardName || ''}</strong> ${award.awardDate || ''}</p>
          <p>${award.awardDesc || ''}</p>
        </div>
      `).join('');
    },
    
    renderInterests: function(interests) {
      if (!interests || interests.length === 0) return '';
      
      return interests.map((interest) => `
        <div class="interest-item">
          <p>${interest.interestName || ''}</p>
          <p>${interest.interestDesc || ''}</p>
        </div>
      `).join('');
    },
    
    renderEvaluation: function(evaluations) {
      if (!evaluations || evaluations.length === 0) return '';
      
      return evaluations.map((eval, index) => `
        <div class="evaluation-item">
          <p>${index + 1}. ${eval.content || ''}</p>
        </div>
      `).join('');
    },
    
    renderCustom: function(custom, index) {
      if (!custom) return '';
      
      return `
        <div class="custom-item">
          <p><strong>${custom.role || ''}</strong> ${custom.startDate || ''} - ${custom.endDate || ''}</p>
          <p>${custom.content || ''}</p>
        </div>
      `;
    }
  },
  
  // 可以添加更多模板
  templateA01: {
    // 简历模板A01的HTML和CSS
    html: `
    <div class="resume-container" id="{{containerId}}">
      <!-- 模板A01的HTML结构 -->
      <!-- 这里应该与templateA01组件的结构保持一致 -->
    </div>
    `,
    css: `
      /* 模板A01的CSS样式 */
      /* 这里应该与templateA01组件的样式保持一致 */
    `
  },
  
  templateA02: {
    // 简历模板A02的HTML和CSS
  },
  
  templateA03: {
    // 简历模板A03的HTML和CSS
  }
};

// 导出模板
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { resumeTemplates };
} else {
  // 小程序环境
  export { resumeTemplates };
} 