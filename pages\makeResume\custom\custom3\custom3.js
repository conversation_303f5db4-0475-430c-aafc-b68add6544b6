Page({
  data: {
    custom3FormData: {
      index: '3',
      startDate: '',
      endDate: '',
      customName: '',
      role: '',
      content: ''
    },
    customFormStyle: {
      isBold: false,
      isItalic: false,
      isUnderline: false,
      isList: false
    }
  },

  onLoad(options) {
    // 获取保存的数据
    const savedData = wx.getStorageSync('customContent3') || {};

    this.setData({
      'custom3FormData.startDate': savedData.startDate || '',
      'custom3FormData.endDate': savedData.endDate || '',
      'custom3FormData.customName': savedData.customName || '',
      'custom3FormData.role': savedData.role || '',
      'custom3FormData.content': savedData.content || ''
    });
  },

  // 处理开始日期变化
  handleStartDateChange(e) {
    this.setData({
      'custom3FormData.startDate': e.detail.value
    });
  },

  // 处理结束日期变化
  handleEndDateChange(e) {
    this.setData({
      'custom3FormData.endDate': e.detail.value
    });
  },

  // 设置"至今"
  setToNow() {
    this.setData({
      'custom3FormData.endDate': '至今'
    });
  },

  // 处理名称输入
  handleNameInput(e) {
    this.setData({
      'custom3FormData.customName': e.detail.value
    });
  },

  // 处理角色输入
  handleRoleInput(e) {
    this.setData({
      'custom3FormData.role': e.detail.value
    });
  },

  // 处理内容输入
  handleContentInput(e) {
    this.setData({
      'custom3FormData.content': e.detail.value
    });
  },

  // 文本编辑工具栏功能
  handleBold() {
    this.setData({
      'customFormStyle.isBold': !this.data.customFormStyle.isBold
    });
  },

  handleItalic() {
    this.setData({
      'customFormStyle.isItalic': !this.data.customFormStyle.isItalic
    });
  },

  handleUnderline() {
    this.setData({
      'customFormStyle.isUnderline': !this.data.customFormStyle.isUnderline
    });
  },

  handleList() {
    this.setData({
      'customFormStyle.isList': !this.data.customFormStyle.isList
    });
  },

  // 保存内容
  saveContent() {
    const { custom3FormData } = this.data;
    const {
      index,
      startDate,
      endDate,
      customName,
      role,
      content
    } = custom3FormData;

    // 添加必填校验
    if (!customName || customName.trim() === '') {
      wx.showToast({
        title: '请输入名称',
        icon: 'none'
      });
      return;
    }

    const saveData = {
      startDate,
      endDate,
      customName,
      role,
      content
    };

    wx.setStorageSync(`customContent${index}`, saveData);
    wx.setStorageSync(`customList${index}`, [saveData]);

    // 同时保存到resumeManager
    const resumeManager = require('../../../../utils/resume/resumeManager.js');
    const currentResumeData = resumeManager.getCurrentResumeData() || {};

    // 更新自定义模块3
    if (!currentResumeData.custom3) {
      currentResumeData.custom3 = [];
    }
    currentResumeData.custom3 = [saveData];

    // 保存到resumeManager
    resumeManager.saveCurrentResumeData(currentResumeData);

    wx.showToast({
      title: '保存成功',
      icon: 'success',
      duration: 1000,
      success: () => {
        setTimeout(() => {
          wx.navigateBack();
        }, 1000);
      }
    });
  },

  // 删除内容
  deleteContent() {
    wx.showModal({
      title: '提示',
      content: '确定要删除内容吗？',
      success: (res) => {
        if (res.confirm) {
          const { index } = this.data.custom3FormData;
          wx.removeStorageSync(`customContent${index}`);
          wx.removeStorageSync(`customList${index}`);
          this.setData({
            custom3FormData: {
              ...this.data.custom3FormData,
              startDate: '',
              endDate: '',
              customName: '',
              role: '',
              content: ''
            }
          });

          wx.showToast({
            title: '已删除',
            icon: 'success',
            duration: 2000,
            success: () => {
              setTimeout(() => {
                wx.navigateBack();
              }, 2000);
            }
          });
        }
      }
    });
  }
});